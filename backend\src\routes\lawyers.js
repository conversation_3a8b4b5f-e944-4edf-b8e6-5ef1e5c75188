const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { 
  User, 
  LawyerProfile, 
  LawyerSpecialty, 
  UserLawyerAuthorization,
  Case 
} = require('../models');
const { auth, adminOnly } = require('../middleware/auth');
const { checkDataAccess } = require('../middleware/dataAccess');
const logger = require('../utils/logger');

const router = express.Router();

// 获取律师专业列表
router.get('/specialties', async (req, res) => {
  try {
    const specialties = await LawyerSpecialty.findActive();
    
    res.json({
      success: true,
      data: { specialties }
    });
  } catch (error) {
    logger.error('获取律师专业列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取律师专业列表失败'
    });
  }
});

// 搜索律师
router.get('/search', [
  query('specialty').optional().isInt().withMessage('专业ID必须是整数'),
  query('location').optional().isLength({ min: 1, max: 50 }).withMessage('地区长度1-50字符'),
  query('minSuccessRate').optional().isFloat({ min: 0, max: 100 }).withMessage('最低胜诉率0-100'),
  query('maxHourlyRate').optional().isFloat({ min: 0 }).withMessage('最高费用必须大于0'),
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须大于0'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量1-50')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    const { 
      specialty, 
      location, 
      minSuccessRate, 
      maxHourlyRate,
      page = 1,
      limit = 10
    } = req.query;

    const offset = (page - 1) * limit;

    const lawyers = await LawyerProfile.searchLawyers({
      specialty,
      location,
      minSuccessRate,
      maxHourlyRate
    }, {
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // 只返回公开信息
    const publicLawyers = lawyers.map(lawyer => ({
      ...lawyer.getPublicProfile(),
      user: {
        id: lawyer.user.id,
        realName: lawyer.user.realName,
        avatar: lawyer.user.avatar
      },
      specialty: lawyer.specialty
    }));

    res.json({
      success: true,
      data: {
        lawyers: publicLawyers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: publicLawyers.length
        }
      }
    });
  } catch (error) {
    logger.error('搜索律师失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索律师失败'
    });
  }
});

// 获取律师详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const lawyer = await LawyerProfile.findOne({
      where: { userId: id, isVerified: true, status: 'verified' },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'realName', 'avatar']
        },
        {
          model: LawyerSpecialty,
          as: 'specialty'
        }
      ]
    });

    if (!lawyer) {
      return res.status(404).json({
        success: false,
        message: '律师不存在或未认证'
      });
    }

    res.json({
      success: true,
      data: {
        lawyer: {
          ...lawyer.getPublicProfile(),
          user: lawyer.user,
          specialty: lawyer.specialty
        }
      }
    });
  } catch (error) {
    logger.error('获取律师详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取律师详情失败'
    });
  }
});

// 申请成为律师
router.post('/apply', auth, [
  body('specialtyId').isInt().withMessage('请选择专业'),
  body('licenseNumber').isLength({ min: 5, max: 100 }).withMessage('执业证号长度5-100字符'),
  body('lawFirm').optional().isLength({ max: 200 }).withMessage('律师事务所名称最多200字符'),
  body('practiceYears').isInt({ min: 0, max: 50 }).withMessage('执业年限0-50年'),
  body('education').optional().isLength({ max: 1000 }).withMessage('教育背景最多1000字符'),
  body('introduction').optional().isLength({ max: 2000 }).withMessage('个人介绍最多2000字符'),
  body('hourlyRate').optional().isFloat({ min: 0 }).withMessage('咨询费用必须大于0')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const {
      specialtyId,
      licenseNumber,
      lawFirm,
      practiceYears,
      education,
      introduction,
      hourlyRate
    } = req.body;

    // 检查是否已经申请过
    const existingProfile = await LawyerProfile.findByUser(userId);
    if (existingProfile) {
      return res.status(400).json({
        success: false,
        message: '您已经申请过律师认证'
      });
    }

    // 检查执业证号是否已存在
    const existingLicense = await LawyerProfile.findOne({
      where: { licenseNumber }
    });
    if (existingLicense) {
      return res.status(400).json({
        success: false,
        message: '该执业证号已被使用'
      });
    }

    // 检查专业是否存在
    const specialty = await LawyerSpecialty.findByPk(specialtyId);
    if (!specialty || !specialty.isActive) {
      return res.status(400).json({
        success: false,
        message: '选择的专业不存在或已停用'
      });
    }

    // 创建律师资料
    const lawyerProfile = await LawyerProfile.create({
      userId,
      specialtyId,
      licenseNumber,
      lawFirm,
      practiceYears,
      education,
      introduction,
      hourlyRate,
      status: 'pending'
    });

    logger.info(`用户申请律师认证: ${req.user.username}`, { 
      userId,
      specialtyId,
      licenseNumber 
    });

    res.status(201).json({
      success: true,
      message: '律师认证申请已提交，请等待管理员审核',
      data: { lawyerProfile: lawyerProfile.getPublicProfile() }
    });
  } catch (error) {
    logger.error('申请律师认证失败:', error);
    res.status(500).json({
      success: false,
      message: '申请律师认证失败'
    });
  }
});

// 更新律师资料
router.put('/profile', auth, [
  body('lawFirm').optional().isLength({ max: 200 }).withMessage('律师事务所名称最多200字符'),
  body('practiceYears').optional().isInt({ min: 0, max: 50 }).withMessage('执业年限0-50年'),
  body('caseCount').optional().isInt({ min: 0 }).withMessage('案件数量必须大于等于0'),
  body('successRate').optional().isFloat({ min: 0, max: 100 }).withMessage('胜诉率0-100'),
  body('education').optional().isLength({ max: 1000 }).withMessage('教育背景最多1000字符'),
  body('achievements').optional().isLength({ max: 2000 }).withMessage('专业成就最多2000字符'),
  body('introduction').optional().isLength({ max: 2000 }).withMessage('个人介绍最多2000字符'),
  body('hourlyRate').optional().isFloat({ min: 0 }).withMessage('咨询费用必须大于0')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const updateData = req.body;

    const lawyerProfile = await LawyerProfile.findByUser(userId);
    if (!lawyerProfile) {
      return res.status(404).json({
        success: false,
        message: '律师资料不存在'
      });
    }

    // 只有已认证的律师才能更新某些字段
    if (lawyerProfile.status !== 'verified') {
      delete updateData.caseCount;
      delete updateData.successRate;
    }

    await lawyerProfile.update(updateData);

    logger.info(`律师更新资料: ${req.user.username}`, { userId, updateData });

    res.json({
      success: true,
      message: '律师资料更新成功',
      data: { lawyerProfile: lawyerProfile.getPublicProfile() }
    });
  } catch (error) {
    logger.error('更新律师资料失败:', error);
    res.status(500).json({
      success: false,
      message: '更新律师资料失败'
    });
  }
});

// 律师认证管理（仅管理员）
router.put('/:id/verify', auth, adminOnly, [
  body('action').isIn(['verify', 'reject', 'suspend']).withMessage('操作类型无效'),
  body('notes').optional().isLength({ max: 500 }).withMessage('备注最多500字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { action, notes } = req.body;
    const adminId = req.user.id;

    const lawyerProfile = await LawyerProfile.findByUser(id);
    if (!lawyerProfile) {
      return res.status(404).json({
        success: false,
        message: '律师资料不存在'
      });
    }

    switch (action) {
      case 'verify':
        await lawyerProfile.verify(adminId);
        break;
      case 'reject':
        await lawyerProfile.reject(adminId);
        break;
      case 'suspend':
        await lawyerProfile.suspend(adminId);
        break;
    }

    logger.info(`管理员${action}律师认证`, { 
      adminId, 
      lawyerId: id, 
      action, 
      notes 
    });

    res.json({
      success: true,
      message: `律师认证${action}成功`,
      data: { lawyerProfile: lawyerProfile.getPublicProfile() }
    });
  } catch (error) {
    logger.error('律师认证操作失败:', error);
    res.status(500).json({
      success: false,
      message: '律师认证操作失败'
    });
  }
});

// 获取律师的授权案件列表
router.get('/my/cases', auth, async (req, res) => {
  try {
    const lawyerId = req.user.id;
    
    // 检查是否是认证律师
    const lawyerProfile = await LawyerProfile.findByUser(lawyerId);
    if (!lawyerProfile || !lawyerProfile.isVerified) {
      return res.status(403).json({
        success: false,
        message: '您不是认证律师'
      });
    }

    const authorizations = await UserLawyerAuthorization.findActiveByLawyer(lawyerId);

    res.json({
      success: true,
      data: { authorizations }
    });
  } catch (error) {
    logger.error('获取律师授权案件失败:', error);
    res.status(500).json({
      success: false,
      message: '获取授权案件失败'
    });
  }
});

module.exports = router;
