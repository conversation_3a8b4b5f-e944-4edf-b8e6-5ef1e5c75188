const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { 
  User, 
  LawyerProfile, 
  UserLawyerAuthorization,
  Case,
  DataAccessLog
} = require('../models');
const { auth } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// 获取用户的律师授权列表
router.get('/my', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const authorizations = await UserLawyerAuthorization.findActiveByUser(userId);

    res.json({
      success: true,
      data: { authorizations }
    });
  } catch (error) {
    logger.error('获取用户授权列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取授权列表失败'
    });
  }
});

// 授权律师访问案件
router.post('/', auth, [
  body('lawyerId').isInt().withMessage('请选择律师'),
  body('caseId').optional().isInt().withMessage('案件ID必须是整数'),
  body('authorizationType').isIn(['full', 'limited', 'view_only']).withMessage('授权类型无效'),
  body('permissions').optional().isObject().withMessage('权限配置必须是对象'),
  body('expiresAt').optional().isISO8601().withMessage('过期时间格式无效'),
  body('notes').optional().isLength({ max: 500 }).withMessage('备注最多500字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const {
      lawyerId,
      caseId,
      authorizationType,
      permissions,
      expiresAt,
      notes
    } = req.body;

    // 检查律师是否存在且已认证
    const lawyerProfile = await LawyerProfile.findByUser(lawyerId);
    if (!lawyerProfile || !lawyerProfile.isVerified || lawyerProfile.status !== 'verified') {
      return res.status(400).json({
        success: false,
        message: '选择的律师不存在或未认证'
      });
    }

    // 如果指定了案件，检查案件是否属于当前用户
    if (caseId) {
      const caseInfo = await Case.findOne({
        where: { id: caseId, userId }
      });
      if (!caseInfo) {
        return res.status(400).json({
          success: false,
          message: '案件不存在或不属于您'
        });
      }

      // 检查律师专业是否匹配案件类型
      const caseType = await caseInfo.getCaseType();
      const requiredSpecialty = caseType.templateFields?.required_specialty;
      if (requiredSpecialty && lawyerProfile.specialty.code !== requiredSpecialty) {
        return res.status(400).json({
          success: false,
          message: '律师专业与案件类型不匹配'
        });
      }
    }

    // 检查是否已存在授权
    const existingAuth = await UserLawyerAuthorization.findByUserAndLawyer(userId, lawyerId, caseId);
    if (existingAuth && existingAuth.status === 'active') {
      return res.status(400).json({
        success: false,
        message: '已存在有效的授权'
      });
    }

    // 设置默认权限
    const defaultPermissions = {
      view_contact: authorizationType !== 'view_only',
      edit_case: authorizationType === 'full',
      generate_document: authorizationType !== 'view_only',
      communicate: true
    };

    const finalPermissions = { ...defaultPermissions, ...permissions };

    // 创建授权
    const authorization = await UserLawyerAuthorization.create({
      userId,
      lawyerId,
      caseId,
      authorizationType,
      permissions: finalPermissions,
      status: 'active',
      authorizedAt: new Date(),
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      notes
    });

    await authorization.activate();

    logger.info(`用户授权律师访问`, { 
      userId, 
      lawyerId, 
      caseId, 
      authorizationType 
    });

    res.status(201).json({
      success: true,
      message: '律师授权成功',
      data: { authorization }
    });
  } catch (error) {
    logger.error('创建律师授权失败:', error);
    res.status(500).json({
      success: false,
      message: '创建律师授权失败'
    });
  }
});

// 更新授权
router.put('/:id', auth, [
  body('authorizationType').optional().isIn(['full', 'limited', 'view_only']).withMessage('授权类型无效'),
  body('permissions').optional().isObject().withMessage('权限配置必须是对象'),
  body('expiresAt').optional().isISO8601().withMessage('过期时间格式无效'),
  body('notes').optional().isLength({ max: 500 }).withMessage('备注最多500字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const userId = req.user.id;
    const updateData = req.body;

    const authorization = await UserLawyerAuthorization.findOne({
      where: { id, userId }
    });

    if (!authorization) {
      return res.status(404).json({
        success: false,
        message: '授权不存在'
      });
    }

    if (updateData.permissions) {
      await authorization.updatePermissions(updateData.permissions);
    }

    if (updateData.expiresAt) {
      await authorization.extend(new Date(updateData.expiresAt));
    }

    if (updateData.authorizationType || updateData.notes) {
      await authorization.update({
        authorizationType: updateData.authorizationType || authorization.authorizationType,
        notes: updateData.notes || authorization.notes
      });
    }

    logger.info(`用户更新律师授权`, { userId, authorizationId: id, updateData });

    res.json({
      success: true,
      message: '授权更新成功',
      data: { authorization }
    });
  } catch (error) {
    logger.error('更新律师授权失败:', error);
    res.status(500).json({
      success: false,
      message: '更新律师授权失败'
    });
  }
});

// 撤销授权
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const authorization = await UserLawyerAuthorization.findOne({
      where: { id, userId }
    });

    if (!authorization) {
      return res.status(404).json({
        success: false,
        message: '授权不存在'
      });
    }

    await authorization.revoke(userId);

    logger.info(`用户撤销律师授权`, { userId, authorizationId: id });

    res.json({
      success: true,
      message: '授权已撤销'
    });
  } catch (error) {
    logger.error('撤销律师授权失败:', error);
    res.status(500).json({
      success: false,
      message: '撤销律师授权失败'
    });
  }
});

// 获取数据访问日志
router.get('/access-logs', auth, [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须大于0'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量1-50'),
  query('resourceType').optional().isIn(['case', 'user_info', 'contact', 'document']).withMessage('资源类型无效'),
  query('accessResult').optional().isIn(['success', 'denied', 'error']).withMessage('访问结果无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { 
      page = 1, 
      limit = 20, 
      resourceType, 
      accessResult 
    } = req.query;

    const offset = (page - 1) * limit;

    const { count, rows: logs } = await DataAccessLog.getUserAccessHistory(userId, {
      limit: parseInt(limit),
      offset: parseInt(offset),
      resourceType,
      accessResult
    });

    res.json({
      success: true,
      data: {
        logs,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    logger.error('获取访问日志失败:', error);
    res.status(500).json({
      success: false,
      message: '获取访问日志失败'
    });
  }
});

// 获取访问统计
router.get('/access-stats', auth, [
  query('timeRange').optional().isIn(['1d', '7d', '30d', '90d']).withMessage('时间范围无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { timeRange = '7d' } = req.query;

    const stats = await DataAccessLog.getAccessStats(userId, timeRange);

    res.json({
      success: true,
      data: { stats, timeRange }
    });
  } catch (error) {
    logger.error('获取访问统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取访问统计失败'
    });
  }
});

// 推荐律师（基于案件类型）
router.get('/recommend/:caseId', auth, async (req, res) => {
  try {
    const { caseId } = req.params;
    const userId = req.user.id;

    // 检查案件是否属于当前用户
    const caseInfo = await Case.findOne({
      where: { id: caseId, userId },
      include: [
        {
          model: require('../models').CaseType,
          as: 'caseType'
        }
      ]
    });

    if (!caseInfo) {
      return res.status(404).json({
        success: false,
        message: '案件不存在'
      });
    }

    // 获取案件所需专业
    const requiredSpecialty = caseInfo.caseType.templateFields?.required_specialty;
    if (!requiredSpecialty) {
      return res.status(400).json({
        success: false,
        message: '案件类型未配置专业要求'
      });
    }

    // 查找对应专业的律师
    const specialty = await require('../models').LawyerSpecialty.findOne({
      where: { code: requiredSpecialty }
    });

    if (!specialty) {
      return res.status(400).json({
        success: false,
        message: '未找到对应专业'
      });
    }

    const recommendedLawyers = await LawyerProfile.findBySpecialty(specialty.id, {
      limit: 10
    });

    // 只返回公开信息
    const publicLawyers = recommendedLawyers.map(lawyer => ({
      ...lawyer.getPublicProfile(),
      user: {
        id: lawyer.user.id,
        realName: lawyer.user.realName,
        avatar: lawyer.user.avatar
      },
      specialty: lawyer.specialty
    }));

    res.json({
      success: true,
      data: {
        caseInfo: {
          id: caseInfo.id,
          title: caseInfo.title,
          caseType: caseInfo.caseType.name
        },
        requiredSpecialty: specialty,
        recommendedLawyers: publicLawyers
      }
    });
  } catch (error) {
    logger.error('获取推荐律师失败:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐律师失败'
    });
  }
});

module.exports = router;
