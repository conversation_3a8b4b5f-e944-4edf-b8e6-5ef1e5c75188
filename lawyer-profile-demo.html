<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>律师个人主页 - 张律师</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .profile-header {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        .verified-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .profile-main {
            display: flex;
            align-items: flex-start;
            gap: 30px;
            margin-bottom: 30px;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3em;
            font-weight: bold;
            flex-shrink: 0;
        }

        .profile-info h1 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .profile-info .specialty {
            background: #3498db;
            color: white;
            padding: 6px 15px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 15px;
        }

        .profile-info .firm {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 10px;
        }

        .profile-stats {
            display: flex;
            gap: 30px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .pricing-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 5px solid #e74c3c;
        }

        .pricing-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
        }

        .pricing-title {
            font-size: 1.8em;
            color: #2c3e50;
            font-weight: bold;
        }

        .pricing-required {
            background: #e74c3c;
            color: white;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .pricing-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .pricing-card:hover {
            border-color: #3498db;
            transform: translateY(-3px);
        }

        .pricing-card.featured {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
        }

        .pricing-card h3 {
            font-size: 1.1em;
            margin-bottom: 10px;
            color: inherit;
        }

        .pricing-card .price {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .pricing-card .unit {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .pricing-terms {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .pricing-terms h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .pricing-terms ul {
            list-style: none;
            padding: 0;
        }

        .pricing-terms li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .pricing-terms li:before {
            content: "•";
            color: #f39c12;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .services-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .service-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .service-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #3498db;
        }

        .service-item h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .service-item p {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .success-cases {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .case-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #27ae60;
        }

        .case-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .case-result {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 0.9em;
        }

        .case-amount {
            color: #27ae60;
            font-weight: bold;
        }

        .contact-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-white {
            background: white;
            color: #667eea;
        }

        .btn-white:hover {
            background: #f8f9fa;
        }

        .rating-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .rating-overview {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-bottom: 25px;
        }

        .rating-score {
            text-align: center;
        }

        .rating-score .score {
            font-size: 3em;
            font-weight: bold;
            color: #f39c12;
        }

        .rating-score .stars {
            font-size: 1.5em;
            color: #f39c12;
            margin: 10px 0;
        }

        .rating-breakdown {
            flex: 1;
        }

        .rating-bar {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .rating-bar .label {
            width: 80px;
            font-size: 0.9em;
        }

        .rating-bar .bar {
            flex: 1;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            margin: 0 10px;
            overflow: hidden;
        }

        .rating-bar .fill {
            height: 100%;
            background: #f39c12;
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .profile-main {
                flex-direction: column;
                text-align: center;
            }
            
            .profile-stats {
                justify-content: center;
            }
            
            .pricing-grid {
                grid-template-columns: 1fr;
            }
            
            .rating-overview {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 律师基本信息 -->
        <div class="profile-header">
            <div class="verified-badge">✓ 认证律师</div>
            
            <div class="profile-main">
                <div class="profile-avatar">张</div>
                <div class="profile-info">
                    <h1>张律师</h1>
                    <div class="specialty">合同法专业律师</div>
                    <div class="firm">北京市某某律师事务所</div>
                    <p>专注合同法领域6年，擅长处理各类合同纠纷，具有丰富的诉讼和仲裁经验。成功代理案件156起，为客户挽回经济损失超过2000万元。</p>
                </div>
            </div>

            <div class="profile-stats">
                <div class="stat-item">
                    <div class="stat-number">6年</div>
                    <div class="stat-label">执业经验</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">156</div>
                    <div class="stat-label">代理案件</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">85.5%</div>
                    <div class="stat-label">胜诉率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2000万+</div>
                    <div class="stat-label">追回金额</div>
                </div>
            </div>
        </div>

        <!-- 收费标准（强制显示） -->
        <div class="pricing-section">
            <div class="pricing-header">
                <h2 class="pricing-title">💰 收费标准</h2>
                <div class="pricing-required">必须公开</div>
            </div>

            <div class="pricing-grid">
                <div class="pricing-card featured">
                    <h3>咨询服务</h3>
                    <div class="price">800</div>
                    <div class="unit">元/小时</div>
                </div>

                <div class="pricing-card">
                    <h3>法律文书</h3>
                    <div class="price">1,500</div>
                    <div class="unit">元/份</div>
                </div>

                <div class="pricing-card">
                    <h3>出庭代理</h3>
                    <div class="price">3,000</div>
                    <div class="unit">元/次</div>
                </div>

                <div class="pricing-card">
                    <h3>悬赏案件</h3>
                    <div class="price">25%</div>
                    <div class="unit">胜诉后分成</div>
                </div>
            </div>

            <div class="pricing-terms">
                <h4>📋 付费条款</h4>
                <ul>
                    <li>咨询费当场支付，代理费可分期支付</li>
                    <li>悬赏案件0前期费用，胜诉后按约定比例分成</li>
                    <li>败诉案件不收取代理费（悬赏案件）</li>
                    <li>连续委托案件享受9折优惠</li>
                    <li>困难群体可申请法律援助</li>
                </ul>
            </div>
        </div>

        <!-- 专业服务 -->
        <div class="services-section">
            <h2 class="section-title">🎯 专业服务</h2>
            <div class="service-list">
                <div class="service-item">
                    <h4>合同起草与审查</h4>
                    <p>专业起草各类合同，全面审查合同条款，识别法律风险，保障您的合法权益。</p>
                </div>
                <div class="service-item">
                    <h4>合同纠纷代理</h4>
                    <p>代理合同违约、合同解除、损害赔偿等各类合同纠纷案件，维护委托人合法权益。</p>
                </div>
                <div class="service-item">
                    <h4>商务谈判支持</h4>
                    <p>提供商务谈判法律支持，协助制定谈判策略，确保交易安全合规。</p>
                </div>
                <div class="service-item">
                    <h4>法律风险防控</h4>
                    <p>为企业提供合同管理体系建设，建立法律风险防控机制，预防合同纠纷。</p>
                </div>
            </div>
        </div>

        <!-- 胜诉案例 -->
        <div class="success-cases">
            <h2 class="section-title">🏆 胜诉案例</h2>
            
            <div class="case-item">
                <div class="case-title">装修合同纠纷胜诉案例</div>
                <p>某业主因装修质量问题起诉装修公司，经过专业的质量鉴定和法律论证，成功为客户追回装修费用并获得额外赔偿。</p>
                <div class="case-result">
                    <span>案件周期：45天</span>
                    <span class="case-amount">获赔金额：9.5万元</span>
                </div>
            </div>

            <div class="case-item">
                <div class="case-title">买卖合同违约赔偿案</div>
                <p>某采购商因供应商延期交货并提供不合格产品起诉，通过详实的证据收集和精准的法律适用，成功为客户挽回大部分经济损失。</p>
                <div class="case-result">
                    <span>案件周期：60天</span>
                    <span class="case-amount">获赔金额：38万元</span>
                </div>
            </div>

            <div class="case-item">
                <div class="case-title">服务合同纠纷调解案</div>
                <p>某企业因服务质量问题与服务商产生纠纷，通过专业调解成功达成和解协议，为客户节省了诉讼成本。</p>
                <div class="case-result">
                    <span>案件周期：20天</span>
                    <span class="case-amount">获赔金额：15万元</span>
                </div>
            </div>
        </div>

        <!-- 客户评价 -->
        <div class="rating-section">
            <h2 class="section-title">⭐ 客户评价</h2>
            
            <div class="rating-overview">
                <div class="rating-score">
                    <div class="score">4.9</div>
                    <div class="stars">★★★★★</div>
                    <div>基于38条评价</div>
                </div>
                
                <div class="rating-breakdown">
                    <div class="rating-bar">
                        <span class="label">专业能力</span>
                        <div class="bar">
                            <div class="fill" style="width: 95%"></div>
                        </div>
                        <span>4.8</span>
                    </div>
                    <div class="rating-bar">
                        <span class="label">服务态度</span>
                        <div class="bar">
                            <div class="fill" style="width: 98%"></div>
                        </div>
                        <span>4.9</span>
                    </div>
                    <div class="rating-bar">
                        <span class="label">办事效率</span>
                        <div class="bar">
                            <div class="fill" style="width: 90%"></div>
                        </div>
                        <span>4.5</span>
                    </div>
                    <div class="rating-bar">
                        <span class="label">沟通能力</span>
                        <div class="bar">
                            <div class="fill" style="width: 100%"></div>
                        </div>
                        <span>5.0</span>
                    </div>
                </div>
            </div>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 20px;">
                <p><strong>最新评价：</strong>"张律师非常专业，对案件分析透彻，最终帮我成功维权，收费也很合理，强烈推荐！" - 王先生</p>
            </div>
        </div>

        <!-- 联系咨询 -->
        <div class="contact-section">
            <h2>开始您的法律咨询</h2>
            <p>专业的法律服务，合理的收费标准，为您的权益保驾护航</p>
            <div style="margin-top: 30px;">
                <button class="btn btn-white" onclick="consultLawyer()">💬 在线咨询 (800元/小时)</button>
                <button class="btn btn-success" onclick="hireLawyer()">📋 委托代理</button>
                <button class="btn btn-primary" onclick="calculateFee()">💰 费用计算器</button>
            </div>
        </div>
    </div>

    <script>
        function consultLawyer() {
            alert('在线咨询服务：\n\n💰 收费标准\n- 咨询费：800元/小时\n- 首次咨询可享受15分钟免费\n- 后续按实际咨询时间计费\n\n📞 咨询方式\n- 在线文字咨询\n- 语音通话咨询\n- 视频会议咨询\n\n⏰ 服务时间\n- 工作日：9:00-18:00\n- 紧急情况可预约加班');
        }

        function hireLawyer() {
            alert('委托代理服务：\n\n📋 服务流程\n1. 案件初步评估（免费）\n2. 签署委托代理协议\n3. 制定诉讼策略\n4. 收集整理证据\n5. 法庭代理出庭\n6. 执行阶段跟进\n\n💰 收费方式\n- 传统收费：预付代理费\n- 悬赏收费：胜诉后分成\n- 混合收费：部分预付+胜诉分成');
        }

        function calculateFee() {
            const serviceType = prompt('请选择服务类型：\n1. 咨询服务\n2. 文书代写\n3. 诉讼代理\n4. 悬赏案件\n\n请输入数字(1-4):');
            
            let result = '';
            switch(serviceType) {
                case '1':
                    const hours = prompt('请输入咨询时长（小时）:');
                    if (hours && !isNaN(hours)) {
                        const fee = hours * 800;
                        result = `咨询费用计算：\n时长：${hours}小时\n费率：800元/小时\n总费用：${fee}元`;
                    }
                    break;
                case '2':
                    const docs = prompt('请输入文书数量:');
                    if (docs && !isNaN(docs)) {
                        const fee = docs * 1500;
                        result = `文书费用计算：\n数量：${docs}份\n单价：1500元/份\n总费用：${fee}元`;
                    }
                    break;
                case '3':
                    result = '诉讼代理费用：\n基础代理费：根据案件复杂程度\n出庭费：3000元/次\n文书费：1500元/份\n\n具体费用需要根据案件情况评估';
                    break;
                case '4':
                    const amount = prompt('请输入预期获赔金额（元）:');
                    if (amount && !isNaN(amount)) {
                        const fee = amount * 0.25;
                        result = `悬赏案件费用计算：\n获赔金额：${amount}元\n分成比例：25%\n律师费：${fee}元\n\n注意：败诉不收费`;
                    }
                    break;
                default:
                    result = '请输入有效的选项';
            }
            
            if (result) {
                alert(result);
            }
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.profile-header, .pricing-section, .services-section, .success-cases, .rating-section, .contact-section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
