const { Sequelize } = require('sequelize');
const config = require('../config/database').default;

// 创建Sequelize实例
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    port: config.port,
    dialect: config.dialect,
    logging: config.logging,
    pool: config.pool,
    timezone: '+08:00',
    define: {
      timestamps: true,
      underscored: false,
      freezeTableName: true
    }
  }
);

// 导入模型
const User = require('./User')(sequelize, Sequelize.DataTypes);
const CaseType = require('./CaseType')(sequelize, Sequelize.DataTypes);
const Case = require('./Case')(sequelize, Sequelize.DataTypes);
const Template = require('./Template')(sequelize, Sequelize.DataTypes);
const Document = require('./Document')(sequelize, Sequelize.DataTypes);
const SystemConfig = require('./SystemConfig')(sequelize, Sequelize.DataTypes);
const OperationLog = require('./OperationLog')(sequelize, Sequelize.DataTypes);
const FileUpload = require('./FileUpload')(sequelize, Sequelize.DataTypes);

// 定义关联关系
const models = {
  User,
  CaseType,
  Case,
  Template,
  Document,
  SystemConfig,
  OperationLog,
  FileUpload
};

// 用户关联
User.hasMany(Case, { foreignKey: 'userId', as: 'cases' });
User.hasMany(Template, { foreignKey: 'createdBy', as: 'templates' });
User.hasMany(OperationLog, { foreignKey: 'userId', as: 'operationLogs' });
User.hasMany(FileUpload, { foreignKey: 'userId', as: 'fileUploads' });

// 案件类型关联
CaseType.hasMany(Case, { foreignKey: 'caseTypeId', as: 'cases' });
CaseType.hasMany(Template, { foreignKey: 'caseTypeId', as: 'templates' });

// 案件关联
Case.belongsTo(User, { foreignKey: 'userId', as: 'user' });
Case.belongsTo(CaseType, { foreignKey: 'caseTypeId', as: 'caseType' });
Case.hasMany(Document, { foreignKey: 'caseId', as: 'documents' });

// 模板关联
Template.belongsTo(CaseType, { foreignKey: 'caseTypeId', as: 'caseType' });
Template.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });
Template.hasMany(Document, { foreignKey: 'templateId', as: 'documents' });

// 文档关联
Document.belongsTo(Case, { foreignKey: 'caseId', as: 'case' });
Document.belongsTo(Template, { foreignKey: 'templateId', as: 'template' });

// 操作日志关联
OperationLog.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// 文件上传关联
FileUpload.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// 初始化关联
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

module.exports = {
  sequelize,
  Sequelize,
  ...models
};
