module.exports = (sequelize, DataTypes) => {
  const Document = sequelize.define('Document', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    caseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'case_id'
    },
    templateId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'template_id'
    },
    fileName: {
      type: DataTypes.STRING(255),
      allowNull: false,
      field: 'file_name'
    },
    filePath: {
      type: DataTypes.STRING(500),
      allowNull: false,
      field: 'file_path'
    },
    fileType: {
      type: DataTypes.ENUM('docx', 'pdf', 'html'),
      allowNull: false,
      field: 'file_type'
    },
    fileSize: {
      type: DataTypes.INTEGER,
      field: 'file_size'
    },
    content: {
      type: DataTypes.TEXT('long')
    },
    generationParams: {
      type: DataTypes.JSON,
      field: 'generation_params',
      defaultValue: {}
    },
    status: {
      type: DataTypes.ENUM('generating', 'completed', 'failed'),
      defaultValue: 'generating'
    },
    errorMessage: {
      type: DataTypes.TEXT,
      field: 'error_message'
    },
    downloadCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'download_count'
    },
    generatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'generated_at'
    },
    expiresAt: {
      type: DataTypes.DATE,
      field: 'expires_at'
    }
  }, {
    tableName: 'documents',
    timestamps: false
  });

  // 关联定义
  Document.associate = function(models) {
    Document.belongsTo(models.Case, {
      foreignKey: 'caseId',
      as: 'case'
    });
    Document.belongsTo(models.Template, {
      foreignKey: 'templateId',
      as: 'template'
    });
  };

  return Document;
};
