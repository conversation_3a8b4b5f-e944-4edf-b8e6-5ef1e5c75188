<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>起诉书生成系统 - 系统预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .system-overview {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 24px;
            color: white;
        }

        .feature-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }

        .tech-stack {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .tech-category {
            margin-bottom: 25px;
        }

        .tech-category h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .tech-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 2px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .architecture-diagram {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .diagram {
            max-width: 100%;
            height: auto;
            margin: 20px 0;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
            margin-left: 10px;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-progress {
            background: #fff3cd;
            color: #856404;
        }

        .status-planned {
            background: #f8d7da;
            color: #721c24;
        }

        .file-structure {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }

        .file-tree {
            list-style: none;
            padding-left: 0;
        }

        .file-tree li {
            margin: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .file-tree li:before {
            content: "📁";
            position: absolute;
            left: 0;
        }

        .file-tree li.file:before {
            content: "📄";
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>起诉书生成系统</h1>
            <p>专业的法律文书生成平台 - 前后端分离架构</p>
        </div>

        <div class="system-overview">
            <h2>系统概述</h2>
            <p>这是一个完整的前后端分离的起诉书生成系统，支持多种案件类型，具备用户管理、模板管理、文档生成等完整功能。系统采用现代化的技术栈，提供专业、高效、安全的法律文书生成服务。</p>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">⚖️</div>
                <div class="feature-title">多案件类型支持</div>
                <div class="feature-desc">支持民事、劳动、婚姻家庭、知识产权、行政等多种案件类型，每种类型都有专业的模板和字段配置。</div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <div class="feature-title">用户权限管理</div>
                <div class="feature-desc">完善的用户角色体系，支持管理员、律师、普通用户三种角色，精细化权限控制。</div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📝</div>
                <div class="feature-title">智能模板系统</div>
                <div class="feature-desc">可视化模板编辑器，支持变量替换、条件渲染，模板版本管理，满足不同场景需求。</div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📄</div>
                <div class="feature-title">多格式文档生成</div>
                <div class="feature-desc">支持Word、PDF、HTML多种格式输出，在线预览，批量生成，满足不同使用场景。</div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🔒</div>
                <div class="feature-title">数据安全保障</div>
                <div class="feature-desc">JWT认证、数据加密、操作日志、权限控制等多重安全措施，保障用户数据安全。</div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">统计分析功能</div>
                <div class="feature-desc">案件统计、用户行为分析、系统使用报告，为管理决策提供数据支持。</div>
            </div>
        </div>

        <div class="tech-stack">
            <h2>技术架构</h2>
            
            <div class="tech-category">
                <h3>后端技术栈 <span class="status-badge status-completed">已完成</span></h3>
                <div class="tech-tags">
                    <span class="tech-tag">Node.js</span>
                    <span class="tech-tag">Express</span>
                    <span class="tech-tag">MySQL</span>
                    <span class="tech-tag">Sequelize</span>
                    <span class="tech-tag">JWT</span>
                    <span class="tech-tag">Winston</span>
                    <span class="tech-tag">Multer</span>
                </div>
            </div>

            <div class="tech-category">
                <h3>前端技术栈 <span class="status-badge status-progress">开发中</span></h3>
                <div class="tech-tags">
                    <span class="tech-tag">Vue.js 3</span>
                    <span class="tech-tag">Element Plus</span>
                    <span class="tech-tag">Vite</span>
                    <span class="tech-tag">Pinia</span>
                    <span class="tech-tag">Vue Router</span>
                    <span class="tech-tag">Axios</span>
                    <span class="tech-tag">Quill.js</span>
                </div>
            </div>

            <div class="tech-category">
                <h3>文档生成 <span class="status-badge status-planned">计划中</span></h3>
                <div class="tech-tags">
                    <span class="tech-tag">docx</span>
                    <span class="tech-tag">html-pdf</span>
                    <span class="tech-tag">Handlebars</span>
                    <span class="tech-tag">Sharp</span>
                </div>
            </div>

            <div class="tech-category">
                <h3>部署运维 <span class="status-badge status-planned">计划中</span></h3>
                <div class="tech-tags">
                    <span class="tech-tag">Docker</span>
                    <span class="tech-tag">Nginx</span>
                    <span class="tech-tag">PM2</span>
                    <span class="tech-tag">Redis</span>
                </div>
            </div>
        </div>

        <div class="architecture-diagram">
            <h2>系统架构图</h2>
            <div style="background: #f8f9fa; padding: 30px; border-radius: 10px; margin: 20px 0;">
                <div style="text-align: left; font-family: monospace; font-size: 14px; line-height: 1.8;">
                    <div style="text-align: center; margin-bottom: 20px; font-weight: bold;">前后端分离架构</div>
                    
                    <div style="border: 2px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 8px; background: #e3f2fd;">
                        <strong>前端层 (Frontend)</strong><br>
                        ├── Vue.js 3 管理后台<br>
                        ├── Element Plus UI组件<br>
                        ├── 用户前台系统<br>
                        └── 响应式移动端
                    </div>
                    
                    <div style="text-align: center; margin: 15px 0;">⬇️ HTTP/HTTPS ⬇️</div>
                    
                    <div style="border: 2px solid #28a745; padding: 15px; margin: 10px 0; border-radius: 8px; background: #e8f5e8;">
                        <strong>API网关层 (API Gateway)</strong><br>
                        ├── Express.js 服务器<br>
                        ├── 路由管理<br>
                        ├── 中间件处理<br>
                        └── 请求验证
                    </div>
                    
                    <div style="text-align: center; margin: 15px 0;">⬇️ 内部调用 ⬇️</div>
                    
                    <div style="border: 2px solid #ffc107; padding: 15px; margin: 10px 0; border-radius: 8px; background: #fff8e1;">
                        <strong>业务服务层 (Business Services)</strong><br>
                        ├── 用户管理服务<br>
                        ├── 案件管理服务<br>
                        ├── 模板管理服务<br>
                        └── 文档生成服务
                    </div>
                    
                    <div style="text-align: center; margin: 15px 0;">⬇️ ORM映射 ⬇️</div>
                    
                    <div style="border: 2px solid #dc3545; padding: 15px; margin: 10px 0; border-radius: 8px; background: #ffeaea;">
                        <strong>数据存储层 (Data Storage)</strong><br>
                        ├── MySQL 主数据库<br>
                        ├── Redis 缓存<br>
                        ├── 文件存储系统<br>
                        └── 日志存储
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>项目文件结构</h2>
            <div class="file-structure">
                <ul class="file-tree">
                    <li>falvweb/
                        <ul class="file-tree">
                            <li class="file">system-architecture.md</li>
                            <li class="file">system-preview.html</li>
                            <li class="file">index.html (简单版本)</li>
                            <li class="file">example.html</li>
                            <li>database/
                                <ul class="file-tree">
                                    <li class="file">schema.sql</li>
                                </ul>
                            </li>
                            <li>backend/
                                <ul class="file-tree">
                                    <li class="file">package.json</li>
                                    <li class="file">.env.example</li>
                                    <li>src/
                                        <ul class="file-tree">
                                            <li class="file">app.js</li>
                                            <li>config/
                                                <ul class="file-tree">
                                                    <li class="file">database.js</li>
                                                </ul>
                                            </li>
                                            <li>models/
                                                <ul class="file-tree">
                                                    <li class="file">index.js</li>
                                                    <li class="file">User.js</li>
                                                    <li class="file">Case.js</li>
                                                    <li class="file">CaseType.js</li>
                                                    <li class="file">Template.js</li>
                                                    <li class="file">Document.js</li>
                                                </ul>
                                            </li>
                                            <li>routes/
                                                <ul class="file-tree">
                                                    <li class="file">auth.js</li>
                                                    <li class="file">users.js</li>
                                                </ul>
                                            </li>
                                            <li>middleware/
                                                <ul class="file-tree">
                                                    <li class="file">auth.js</li>
                                                    <li class="file">errorHandler.js</li>
                                                </ul>
                                            </li>
                                            <li>utils/
                                                <ul class="file-tree">
                                                    <li class="file">logger.js</li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                            <li>frontend/ (计划中)
                                <ul class="file-tree">
                                    <li class="file">package.json</li>
                                    <li>src/
                                        <ul class="file-tree">
                                            <li class="file">main.js</li>
                                            <li class="file">App.vue</li>
                                            <li>views/</li>
                                            <li>components/</li>
                                            <li>stores/</li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2>功能演示</h2>
            <p>体验不同功能模块，了解系统的完整功能。</p>
            
            <div class="demo-buttons">
                <a href="index.html" class="btn btn-primary">简单版起诉书生成</a>
                <a href="example.html" class="btn btn-secondary">使用示例</a>
                <a href="system-architecture.md" class="btn btn-secondary">技术文档</a>
                <button class="btn btn-secondary" onclick="alert('前端管理系统开发中...')">管理后台 (开发中)</button>
                <button class="btn btn-secondary" onclick="alert('API文档开发中...')">API文档 (开发中)</button>
            </div>
        </div>

        <div class="system-overview">
            <h2>开发进度</h2>
            <div style="margin: 20px 0;">
                <div style="margin: 10px 0;">
                    <strong>✅ 已完成</strong>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>系统架构设计</li>
                        <li>数据库结构设计</li>
                        <li>后端API基础框架</li>
                        <li>用户认证系统</li>
                        <li>基础模型定义</li>
                        <li>简单版前端界面</li>
                    </ul>
                </div>
                
                <div style="margin: 10px 0;">
                    <strong>🔄 进行中</strong>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>完整的API接口开发</li>
                        <li>Vue.js管理后台</li>
                        <li>案件模板系统</li>
                    </ul>
                </div>
                
                <div style="margin: 10px 0;">
                    <strong>📋 计划中</strong>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>文档生成引擎</li>
                        <li>高级模板编辑器</li>
                        <li>统计分析功能</li>
                        <li>系统部署配置</li>
                        <li>性能优化</li>
                        <li>单元测试</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为特性卡片添加点击效果
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });

            // 添加滚动动画
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 为所有主要元素添加观察
            const animatedElements = document.querySelectorAll('.feature-card, .tech-stack, .demo-section, .architecture-diagram');
            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
