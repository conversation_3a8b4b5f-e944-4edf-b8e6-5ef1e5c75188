module.exports = (sequelize, DataTypes) => {
  const UserLawyerAuthorization = sequelize.define('UserLawyerAuthorization', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id'
    },
    lawyerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'lawyer_id'
    },
    caseId: {
      type: DataTypes.INTEGER,
      field: 'case_id'
    },
    authorizationType: {
      type: DataTypes.ENUM('full', 'limited', 'view_only'),
      defaultValue: 'limited',
      field: 'authorization_type'
    },
    permissions: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    status: {
      type: DataTypes.ENUM('pending', 'active', 'expired', 'revoked'),
      defaultValue: 'pending'
    },
    authorizedAt: {
      type: DataTypes.DATE,
      field: 'authorized_at'
    },
    expiresAt: {
      type: DataTypes.DATE,
      field: 'expires_at'
    },
    revokedAt: {
      type: DataTypes.DATE,
      field: 'revoked_at'
    },
    revokedBy: {
      type: DataTypes.INTEGER,
      field: 'revoked_by'
    },
    notes: {
      type: DataTypes.TEXT
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'updated_at'
    }
  }, {
    tableName: 'user_lawyer_authorizations',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'lawyer_id', 'case_id']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['lawyer_id']
      },
      {
        fields: ['case_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['expires_at']
      }
    ]
  });

  // 实例方法
  UserLawyerAuthorization.prototype.activate = async function() {
    this.status = 'active';
    this.authorizedAt = new Date();
    await this.save();
  };

  UserLawyerAuthorization.prototype.revoke = async function(revokedBy) {
    this.status = 'revoked';
    this.revokedAt = new Date();
    this.revokedBy = revokedBy;
    await this.save();
  };

  UserLawyerAuthorization.prototype.extend = async function(newExpiresAt) {
    this.expiresAt = newExpiresAt;
    await this.save();
  };

  UserLawyerAuthorization.prototype.updatePermissions = async function(newPermissions) {
    this.permissions = { ...this.permissions, ...newPermissions };
    await this.save();
  };

  UserLawyerAuthorization.prototype.isExpired = function() {
    return this.expiresAt && new Date() > this.expiresAt;
  };

  UserLawyerAuthorization.prototype.isActive = function() {
    return this.status === 'active' && !this.isExpired();
  };

  UserLawyerAuthorization.prototype.getPermissionLevel = function() {
    if (!this.isActive()) return 'none';
    
    switch (this.authorizationType) {
      case 'full':
        return 'full';
      case 'limited':
        return 'limited';
      case 'view_only':
        return 'view';
      default:
        return 'none';
    }
  };

  // 类方法
  UserLawyerAuthorization.findActiveByUser = function(userId, options = {}) {
    return this.findAll({
      where: {
        userId,
        status: 'active',
        [sequelize.Op.or]: [
          { expiresAt: null },
          { expiresAt: { [sequelize.Op.gt]: new Date() } }
        ]
      },
      include: [
        {
          model: sequelize.models.User,
          as: 'lawyer',
          attributes: ['id', 'username', 'realName', 'avatar'],
          include: [
            {
              model: sequelize.models.LawyerProfile,
              as: 'lawyerProfile'
            }
          ]
        },
        {
          model: sequelize.models.Case,
          as: 'case',
          attributes: ['id', 'caseNumber', 'title']
        }
      ],
      order: [['authorizedAt', 'DESC']],
      ...options
    });
  };

  UserLawyerAuthorization.findActiveByLawyer = function(lawyerId, options = {}) {
    return this.findAll({
      where: {
        lawyerId,
        status: 'active',
        [sequelize.Op.or]: [
          { expiresAt: null },
          { expiresAt: { [sequelize.Op.gt]: new Date() } }
        ]
      },
      include: [
        {
          model: sequelize.models.User,
          as: 'user',
          attributes: ['id', 'username', 'realName']
        },
        {
          model: sequelize.models.Case,
          as: 'case',
          attributes: ['id', 'caseNumber', 'title', 'status']
        }
      ],
      order: [['authorizedAt', 'DESC']],
      ...options
    });
  };

  UserLawyerAuthorization.findByUserAndLawyer = function(userId, lawyerId, caseId = null) {
    const where = { userId, lawyerId };
    if (caseId) where.caseId = caseId;

    return this.findOne({
      where,
      order: [['caseId', 'DESC']] // 优先特定案件授权
    });
  };

  UserLawyerAuthorization.checkPermission = async function(userId, lawyerId, caseId, permission) {
    const authorization = await this.findByUserAndLawyer(userId, lawyerId, caseId);
    
    if (!authorization || !authorization.isActive()) {
      return false;
    }

    const permissions = authorization.permissions || {};
    return permissions[permission] === true;
  };

  UserLawyerAuthorization.cleanupExpired = async function() {
    const expiredAuthorizations = await this.findAll({
      where: {
        status: 'active',
        expiresAt: { [sequelize.Op.lt]: new Date() }
      }
    });

    for (const auth of expiredAuthorizations) {
      auth.status = 'expired';
      await auth.save();
    }

    return expiredAuthorizations.length;
  };

  // 关联定义
  UserLawyerAuthorization.associate = function(models) {
    UserLawyerAuthorization.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    UserLawyerAuthorization.belongsTo(models.User, {
      foreignKey: 'lawyerId',
      as: 'lawyer'
    });
    UserLawyerAuthorization.belongsTo(models.Case, {
      foreignKey: 'caseId',
      as: 'case'
    });
    UserLawyerAuthorization.belongsTo(models.User, {
      foreignKey: 'revokedBy',
      as: 'revoker'
    });
  };

  return UserLawyerAuthorization;
};
