const { v4: uuidv4 } = require('uuid');

module.exports = (sequelize, DataTypes) => {
  const Case = sequelize.define('Case', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id'
    },
    caseTypeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'case_type_id'
    },
    caseNumber: {
      type: DataTypes.STRING(100),
      unique: true,
      field: 'case_number'
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      validate: {
        len: [2, 200]
      }
    },
    plaintiffInfo: {
      type: DataTypes.JSON,
      allowNull: false,
      field: 'plaintiff_info',
      defaultValue: {}
    },
    defendantInfo: {
      type: DataTypes.JSON,
      allowNull: false,
      field: 'defendant_info',
      defaultValue: {}
    },
    caseDetails: {
      type: DataTypes.JSON,
      allowNull: false,
      field: 'case_details',
      defaultValue: {}
    },
    claims: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: []
    },
    factsAndReasons: {
      type: DataTypes.TEXT,
      field: 'facts_and_reasons'
    },
    evidenceList: {
      type: DataTypes.JSON,
      field: 'evidence_list',
      defaultValue: []
    },
    legalBasis: {
      type: DataTypes.TEXT,
      field: 'legal_basis'
    },
    courtInfo: {
      type: DataTypes.JSON,
      field: 'court_info',
      defaultValue: {}
    },
    status: {
      type: DataTypes.ENUM('draft', 'completed', 'submitted', 'archived'),
      defaultValue: 'draft'
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
      defaultValue: 'medium'
    },
    tags: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    notes: {
      type: DataTypes.TEXT
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'updated_at'
    }
  }, {
    tableName: 'cases',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    hooks: {
      beforeCreate: (caseInstance) => {
        if (!caseInstance.caseNumber) {
          const timestamp = Date.now().toString(36);
          const random = Math.random().toString(36).substr(2, 5);
          caseInstance.caseNumber = `CASE-${timestamp}-${random}`.toUpperCase();
        }
      }
    },
    indexes: [
      {
        unique: true,
        fields: ['case_number']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['case_type_id']
      },
      {
        fields: ['status']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  // 实例方法
  Case.prototype.getPlaintiffName = function() {
    return this.plaintiffInfo?.name || '未知';
  };

  Case.prototype.getDefendantName = function() {
    return this.defendantInfo?.name || '未知';
  };

  Case.prototype.getStatusText = function() {
    const statusMap = {
      draft: '草稿',
      completed: '已完成',
      submitted: '已提交',
      archived: '已归档'
    };
    return statusMap[this.status] || this.status;
  };

  Case.prototype.getPriorityText = function() {
    const priorityMap = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    };
    return priorityMap[this.priority] || this.priority;
  };

  Case.prototype.updateStatus = async function(status) {
    this.status = status;
    await this.save();
  };

  Case.prototype.addTag = async function(tag) {
    const tags = this.tags || [];
    if (!tags.includes(tag)) {
      tags.push(tag);
      this.tags = tags;
      await this.save();
    }
  };

  Case.prototype.removeTag = async function(tag) {
    const tags = this.tags || [];
    const index = tags.indexOf(tag);
    if (index > -1) {
      tags.splice(index, 1);
      this.tags = tags;
      await this.save();
    }
  };

  // 类方法
  Case.findByUser = function(userId, options = {}) {
    return this.findAll({
      where: { userId },
      include: [
        { model: sequelize.models.CaseType, as: 'caseType' },
        { model: sequelize.models.User, as: 'user', attributes: ['id', 'username', 'realName'] }
      ],
      order: [['updatedAt', 'DESC']],
      ...options
    });
  };

  Case.findByStatus = function(status, options = {}) {
    return this.findAll({
      where: { status },
      include: [
        { model: sequelize.models.CaseType, as: 'caseType' },
        { model: sequelize.models.User, as: 'user', attributes: ['id', 'username', 'realName'] }
      ],
      order: [['updatedAt', 'DESC']],
      ...options
    });
  };

  Case.findByCaseNumber = function(caseNumber) {
    return this.findOne({
      where: { caseNumber },
      include: [
        { model: sequelize.models.CaseType, as: 'caseType' },
        { model: sequelize.models.User, as: 'user', attributes: ['id', 'username', 'realName'] }
      ]
    });
  };

  Case.getStatusOptions = function() {
    return [
      { value: 'draft', label: '草稿' },
      { value: 'completed', label: '已完成' },
      { value: 'submitted', label: '已提交' },
      { value: 'archived', label: '已归档' }
    ];
  };

  Case.getPriorityOptions = function() {
    return [
      { value: 'low', label: '低' },
      { value: 'medium', label: '中' },
      { value: 'high', label: '高' },
      { value: 'urgent', label: '紧急' }
    ];
  };

  // 关联定义
  Case.associate = function(models) {
    Case.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    Case.belongsTo(models.CaseType, {
      foreignKey: 'caseTypeId',
      as: 'caseType'
    });
    Case.hasMany(models.Document, {
      foreignKey: 'caseId',
      as: 'documents'
    });
  };

  return Case;
};
