module.exports = (sequelize, DataTypes) => {
  const CaseType = sequelize.define('CaseType', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100]
      }
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 50],
        isUppercase: true
      }
    },
    description: {
      type: DataTypes.TEXT
    },
    category: {
      type: DataTypes.ENUM('civil', 'labor', 'family', 'intellectual', 'administrative', 'criminal'),
      allowNull: false
    },
    templateFields: {
      type: DataTypes.JSON,
      field: 'template_fields',
      defaultValue: {}
    },
    formConfig: {
      type: DataTypes.JSON,
      field: 'form_config',
      defaultValue: {}
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'sort_order'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'updated_at'
    }
  }, {
    tableName: 'case_types',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['code']
      },
      {
        fields: ['category']
      },
      {
        fields: ['is_active']
      },
      {
        fields: ['sort_order']
      }
    ]
  });

  // 实例方法
  CaseType.prototype.getFormFields = function() {
    return this.templateFields || {};
  };

  CaseType.prototype.getFormConfig = function() {
    return this.formConfig || {};
  };

  CaseType.prototype.activate = async function() {
    this.isActive = true;
    await this.save();
  };

  CaseType.prototype.deactivate = async function() {
    this.isActive = false;
    await this.save();
  };

  // 类方法
  CaseType.findActive = function() {
    return this.findAll({
      where: { isActive: true },
      order: [['sortOrder', 'ASC'], ['name', 'ASC']]
    });
  };

  CaseType.findByCategory = function(category) {
    return this.findAll({
      where: { category, isActive: true },
      order: [['sortOrder', 'ASC'], ['name', 'ASC']]
    });
  };

  CaseType.findByCode = function(code) {
    return this.findOne({ where: { code } });
  };

  CaseType.getCategories = function() {
    return [
      { value: 'civil', label: '民事案件' },
      { value: 'labor', label: '劳动案件' },
      { value: 'family', label: '婚姻家庭案件' },
      { value: 'intellectual', label: '知识产权案件' },
      { value: 'administrative', label: '行政案件' },
      { value: 'criminal', label: '刑事案件' }
    ];
  };

  // 关联定义
  CaseType.associate = function(models) {
    CaseType.hasMany(models.Case, {
      foreignKey: 'caseTypeId',
      as: 'cases'
    });
    CaseType.hasMany(models.Template, {
      foreignKey: 'caseTypeId',
      as: 'templates'
    });
  };

  return CaseType;
};
