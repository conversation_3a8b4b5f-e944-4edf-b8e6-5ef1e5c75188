* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5em;
}

header p {
    color: #7f8c8d;
    font-size: 1.1em;
}

.lawsuit-form {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-section {
    margin-bottom: 40px;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 30px;
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 20px;
}

.form-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5em;
    border-left: 4px solid #3498db;
    padding-left: 15px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.form-group input[required],
.form-group select[required],
.form-group textarea[required] {
    border-left: 4px solid #e74c3c;
}

.form-group input[required]:valid,
.form-group select[required]:valid,
.form-group textarea[required]:valid {
    border-left: 4px solid #27ae60;
}

.form-actions {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #ecf0f1;
}

.btn-primary,
.btn-secondary {
    padding: 12px 30px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
    transform: translateY(-2px);
}

.result-section {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-top: 30px;
}

.result-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.result-actions {
    text-align: center;
    margin-bottom: 30px;
}

.lawsuit-content {
    background: #fafafa;
    padding: 30px;
    border-radius: 4px;
    border: 1px solid #ddd;
    font-family: 'SimSun', serif;
    line-height: 2;
    white-space: pre-wrap;
}

.lawsuit-content h3 {
    text-align: center;
    font-size: 24px;
    margin-bottom: 30px;
    color: #2c3e50;
}

.lawsuit-content .court-info {
    text-align: right;
    margin-bottom: 20px;
    font-weight: bold;
}

.lawsuit-content .party-info {
    margin-bottom: 20px;
    line-height: 1.8;
}

.lawsuit-content .section-title {
    font-weight: bold;
    margin: 30px 0 15px 0;
    color: #2c3e50;
}

.lawsuit-content .signature-area {
    margin-top: 50px;
    text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header {
        padding: 20px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .lawsuit-form {
        padding: 20px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 14px;
    }
    
    .btn-primary,
    .btn-secondary {
        padding: 10px 20px;
        font-size: 14px;
        margin: 5px;
    }
    
    .lawsuit-content {
        padding: 20px;
        font-size: 14px;
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
    }
    
    .container {
        max-width: none;
        margin: 0;
        padding: 0;
    }
    
    header,
    .lawsuit-form,
    .result-actions {
        display: none;
    }
    
    .result-section {
        box-shadow: none;
        padding: 0;
        margin: 0;
    }
    
    .lawsuit-content {
        background: white;
        border: none;
        padding: 20px;
        font-size: 12pt;
        line-height: 1.8;
    }
}
