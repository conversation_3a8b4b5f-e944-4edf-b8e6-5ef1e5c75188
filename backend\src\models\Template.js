module.exports = (sequelize, DataTypes) => {
  const Template = sequelize.define('Template', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    caseTypeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'case_type_id'
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100]
      }
    },
    description: {
      type: DataTypes.TEXT
    },
    content: {
      type: DataTypes.TEXT('long'),
      allowNull: false
    },
    variables: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    version: {
      type: DataTypes.STRING(20),
      defaultValue: '1.0'
    },
    isDefault: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_default'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      field: 'created_by'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'updated_at'
    }
  }, {
    tableName: 'templates',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  // 关联定义
  Template.associate = function(models) {
    Template.belongsTo(models.CaseType, {
      foreignKey: 'caseTypeId',
      as: 'caseType'
    });
    Template.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
    Template.hasMany(models.Document, {
      foreignKey: 'templateId',
      as: 'documents'
    });
  };

  return Template;
};
