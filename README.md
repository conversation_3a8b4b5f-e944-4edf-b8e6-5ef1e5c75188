# 起诉书生成系统

这是一个专业的起诉书生成系统，可以根据用户填写的信息自动生成符合法律格式要求的起诉书。

## 功能特点

- **智能表单设计**：根据当事人类型（个人/公司/组织）动态显示相关字段
- **专业格式**：生成的起诉书符合中国法院的标准格式要求
- **多种案件类型**：支持合同纠纷、侵权责任、物权纠纷等多种案件类型
- **完整信息收集**：包含原告、被告、案件事实、诉讼请求、法律依据、证据清单等
- **便捷操作**：支持打印、下载、复制等功能
- **响应式设计**：适配桌面和移动设备

## 使用方法

1. 打开 `index.html` 文件
2. 按照表单提示填写相关信息：
   - **原告信息**：姓名、类型、住址等基本信息
   - **被告信息**：被告的基本信息
   - **案件信息**：案件类型、争议金额、发生时间地点等
   - **诉讼请求**：具体的诉讼要求
   - **事实与理由**：案件的详细经过和法律依据
   - **证据清单**：相关证据材料
   - **法院信息**：受理法院名称
3. 点击"生成起诉书"按钮
4. 系统将自动生成专业的起诉书
5. 可以选择打印、下载或复制生成的起诉书

## 支持的案件类型

- 合同纠纷
- 侵权责任纠纷
- 物权纠纷
- 劳动争议
- 婚姻家庭纠纷
- 继承纠纷
- 债权债务纠纷
- 其他类型

## 当事人类型

### 个人
- 需要填写：姓名、性别、年龄、民族、职业、住址、联系电话、身份证号

### 公司/组织
- 需要填写：名称、地址、联系电话、统一社会信用代码

## 生成的起诉书包含

1. **标题**：起诉书
2. **当事人信息**：原告和被告的详细信息
3. **诉讼请求**：具体的诉讼要求（自动编号）
4. **事实与理由**：案件经过和法律依据
5. **证据清单**：相关证据材料（可选）
6. **结尾**：标准的结尾格式
7. **签名区域**：起诉人签名和日期

## 技术特点

- **纯前端实现**：无需服务器，可直接在浏览器中运行
- **表单验证**：确保必填字段完整填写
- **智能格式化**：自动格式化诉讼请求等内容
- **打印优化**：专门的打印样式，确保打印效果
- **数据安全**：所有数据在本地处理，不会上传到服务器

## 文件结构

```
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑文件
└── README.md           # 说明文档
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. **法律免责声明**：本系统生成的起诉书仅供参考，具体的法律文书应当根据实际情况进行调整，建议咨询专业律师。

2. **信息准确性**：请确保填写的信息准确无误，特别是当事人信息、案件事实等关键内容。

3. **法律依据**：建议在"法律依据"部分填写具体的法条，以增强起诉书的说服力。

4. **证据材料**：请确保列出的证据材料真实有效，并在起诉时一并提交。

5. **管辖法院**：请选择有管辖权的法院，通常是被告住所地或合同履行地的法院。

## 使用建议

1. **详细填写**：信息填写越详细，生成的起诉书越专业
2. **事实清楚**：在"事实与理由"部分要条理清晰地叙述案件经过
3. **请求明确**：诉讼请求要具体明确，避免模糊表述
4. **证据充分**：列出所有相关证据，增强案件说服力
5. **专业审查**：生成后建议请专业律师审查修改

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的起诉书生成功能
- 包含完整的表单验证和格式化功能
