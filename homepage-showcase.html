<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>起诉书生成系统 - 胜诉案例与悬赏委托</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section {
            margin: 40px 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: 30px;
        }

        .section-title h2 {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .section-title p {
            font-size: 1.1em;
            color: #7f8c8d;
        }

        .success-cases-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .success-case-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid #27ae60;
        }

        .success-case-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .case-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .case-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .case-type {
            background: #3498db;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .case-parties {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .case-amount {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
            padding: 15px;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border-radius: 8px;
        }

        .amount-item {
            text-align: center;
        }

        .amount-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .amount-value {
            font-size: 1.3em;
            font-weight: bold;
            margin-top: 5px;
        }

        .lawyer-info {
            display: flex;
            align-items: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ecf0f1;
        }

        .lawyer-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 12px;
        }

        .lawyer-details h4 {
            color: #2c3e50;
            margin-bottom: 3px;
        }

        .lawyer-details p {
            color: #7f8c8d;
            font-size: 0.9em;
        }

        .rating {
            margin-left: auto;
            text-align: right;
        }

        .stars {
            color: #f39c12;
            font-size: 1.1em;
        }

        .bounty-cases-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }

        .bounty-case-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            border-left: 5px solid #e74c3c;
            position: relative;
        }

        .bounty-case-card:hover {
            transform: translateY(-3px);
        }

        .bounty-badge {
            position: absolute;
            top: -10px;
            right: 20px;
            background: #e74c3c;
            color: white;
            padding: 8px 15px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .bounty-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .bounty-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }

        .meta-tag {
            background: #ecf0f1;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8em;
            color: #2c3e50;
        }

        .urgency-high {
            background: #e74c3c;
            color: white;
        }

        .urgency-medium {
            background: #f39c12;
            color: white;
        }

        .urgency-low {
            background: #27ae60;
            color: white;
        }

        .bounty-amount {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
        }

        .commission-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ecf0f1;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .stats-section {
            background: white;
            border-radius: 15px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .stat-item {
            padding: 20px;
        }

        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            color: #7f8c8d;
        }

        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
            margin: 40px 0;
            border-radius: 15px;
        }

        .cta-buttons {
            margin-top: 30px;
        }

        .cta-buttons .btn {
            margin: 0 10px;
            padding: 15px 30px;
            font-size: 1.1em;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .success-cases-grid,
            .bounty-cases-grid {
                grid-template-columns: 1fr;
            }
            
            .case-amount {
                flex-direction: column;
                gap: 10px;
            }
            
            .commission-info {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>⚖️ 专业法律服务平台</h1>
            <p>胜诉案例展示 · 0律师费悬赏委托 · 专业律师服务</p>
        </div>
    </div>

    <div class="container">
        <!-- 平台统计 -->
        <div class="stats-section">
            <h2>平台服务数据</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">1,256</div>
                    <div class="stat-label">成功案例</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">89.5%</div>
                    <div class="stat-label">平均胜诉率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3.2亿</div>
                    <div class="stat-label">累计追回金额</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">156</div>
                    <div class="stat-label">认证律师</div>
                </div>
            </div>
        </div>

        <!-- 胜诉案例展示 -->
        <div class="section">
            <div class="section-title">
                <h2>🏆 胜诉案例展示</h2>
                <p>真实案例，专业服务，为您的权益保驾护航</p>
            </div>

            <div class="success-cases-grid">
                <div class="success-case-card">
                    <div class="case-header">
                        <div class="case-type">合同纠纷</div>
                    </div>
                    <div class="case-title">装修合同纠纷胜诉案例</div>
                    
                    <div class="case-parties">
                        <p><strong>争议原因：</strong>装修质量严重不符合合同约定，存在多处质量问题</p>
                        <p><strong>案件概况：</strong>某业主 vs 某装修公司</p>
                    </div>

                    <div class="case-amount">
                        <div class="amount-item">
                            <div class="amount-label">索赔金额</div>
                            <div class="amount-value">12万元</div>
                        </div>
                        <div class="amount-item">
                            <div class="amount-label">获赔金额</div>
                            <div class="amount-value">9.5万元</div>
                        </div>
                        <div class="amount-item">
                            <div class="amount-label">案件周期</div>
                            <div class="amount-value">45天</div>
                        </div>
                    </div>

                    <div class="lawyer-info">
                        <div class="lawyer-avatar">张</div>
                        <div class="lawyer-details">
                            <h4>张律师</h4>
                            <p>北京市某某律师事务所 · 6年执业经验</p>
                        </div>
                        <div class="rating">
                            <div class="stars">★★★★★</div>
                            <p>5.0分 (23评价)</p>
                        </div>
                    </div>
                </div>

                <div class="success-case-card">
                    <div class="case-header">
                        <div class="case-type">合同纠纷</div>
                    </div>
                    <div class="case-title">买卖合同违约赔偿案</div>
                    
                    <div class="case-parties">
                        <p><strong>争议原因：</strong>供应商延期交货并提供不合格产品，造成重大经济损失</p>
                        <p><strong>案件概况：</strong>某采购商 vs 某供应商</p>
                    </div>

                    <div class="case-amount">
                        <div class="amount-item">
                            <div class="amount-label">索赔金额</div>
                            <div class="amount-value">50万元</div>
                        </div>
                        <div class="amount-item">
                            <div class="amount-label">获赔金额</div>
                            <div class="amount-value">38万元</div>
                        </div>
                        <div class="amount-item">
                            <div class="amount-label">案件周期</div>
                            <div class="amount-value">60天</div>
                        </div>
                    </div>

                    <div class="lawyer-info">
                        <div class="lawyer-avatar">张</div>
                        <div class="lawyer-details">
                            <h4>张律师</h4>
                            <p>北京市某某律师事务所 · 6年执业经验</p>
                        </div>
                        <div class="rating">
                            <div class="stars">★★★★☆</div>
                            <p>4.8分 (15评价)</p>
                        </div>
                    </div>
                </div>

                <div class="success-case-card">
                    <div class="case-header">
                        <div class="case-type">劳动纠纷</div>
                    </div>
                    <div class="case-title">劳动争议仲裁胜诉</div>
                    
                    <div class="case-parties">
                        <p><strong>争议原因：</strong>公司违法解除劳动合同，拒付加班费和经济补偿</p>
                        <p><strong>案件概况：</strong>某技术员工 vs 某科技公司</p>
                    </div>

                    <div class="case-amount">
                        <div class="amount-item">
                            <div class="amount-label">索赔金额</div>
                            <div class="amount-value">8.5万元</div>
                        </div>
                        <div class="amount-item">
                            <div class="amount-label">获赔金额</div>
                            <div class="amount-value">7.8万元</div>
                        </div>
                        <div class="amount-item">
                            <div class="amount-label">案件周期</div>
                            <div class="amount-value">30天</div>
                        </div>
                    </div>

                    <div class="lawyer-info">
                        <div class="lawyer-avatar">李</div>
                        <div class="lawyer-details">
                            <h4>李律师</h4>
                            <p>上海市某某律师事务所 · 5年执业经验</p>
                        </div>
                        <div class="rating">
                            <div class="stars">★★★★★</div>
                            <p>5.0分 (31评价)</p>
                        </div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-primary" onclick="alert('查看更多胜诉案例')">查看更多案例</button>
            </div>
        </div>

        <!-- 0律师费悬赏委托 -->
        <div class="section">
            <div class="section-title">
                <h2>💰 0律师费悬赏委托</h2>
                <p>胜诉后付费，降低维权成本，专业律师为您服务</p>
            </div>

            <div class="bounty-cases-grid">
                <div class="bounty-case-card">
                    <div class="bounty-badge">0律师费</div>
                    <div class="bounty-title">商铺租赁合同纠纷求助</div>
                    
                    <div class="bounty-meta">
                        <span class="meta-tag">合同纠纷</span>
                        <span class="meta-tag urgency-high">紧急</span>
                        <span class="meta-tag">北京朝阳</span>
                        <span class="meta-tag">证据充分</span>
                    </div>

                    <p style="margin: 15px 0; color: #7f8c8d;">
                        房东违约提前收回商铺，造成装修损失和营业损失，需要专业律师代理追讨赔偿...
                    </p>

                    <div class="bounty-amount">
                        <div style="font-size: 0.9em; opacity: 0.9;">预期获赔金额</div>
                        <div style="font-size: 1.8em; font-weight: bold; margin-top: 5px;">18万元</div>
                    </div>

                    <div class="commission-info">
                        <div>
                            <strong>律师分成：25%</strong><br>
                            <small>胜诉后支付</small>
                        </div>
                        <div>
                            <button class="btn btn-danger" onclick="applyBountyCase(1)">立即接案</button>
                        </div>
                    </div>
                </div>

                <div class="bounty-case-card">
                    <div class="bounty-badge">0律师费</div>
                    <div class="bounty-title">网络诽谤名誉权维权</div>
                    
                    <div class="bounty-meta">
                        <span class="meta-tag">侵权纠纷</span>
                        <span class="meta-tag urgency-medium">一般</span>
                        <span class="meta-tag">上海浦东</span>
                        <span class="meta-tag">证据一般</span>
                    </div>

                    <p style="margin: 15px 0; color: #7f8c8d;">
                        某自媒体恶意传播不实信息，严重损害个人名誉和商业信誉，需要律师协助维权...
                    </p>

                    <div class="bounty-amount">
                        <div style="font-size: 0.9em; opacity: 0.9;">预期获赔金额</div>
                        <div style="font-size: 1.8em; font-weight: bold; margin-top: 5px;">8万元</div>
                    </div>

                    <div class="commission-info">
                        <div>
                            <strong>律师分成：30%</strong><br>
                            <small>胜诉后支付</small>
                        </div>
                        <div>
                            <button class="btn btn-danger" onclick="applyBountyCase(2)">立即接案</button>
                        </div>
                    </div>
                </div>

                <div class="bounty-case-card">
                    <div class="bounty-badge">0律师费</div>
                    <div class="bounty-title">劳动仲裁代理需求</div>
                    
                    <div class="bounty-meta">
                        <span class="meta-tag">劳动纠纷</span>
                        <span class="meta-tag urgency-medium">一般</span>
                        <span class="meta-tag">深圳南山</span>
                        <span class="meta-tag">证据充分</span>
                    </div>

                    <p style="margin: 15px 0; color: #7f8c8d;">
                        公司恶意调岗降薪，变相逼迫离职，需要专业劳动法律师代理仲裁...
                    </p>

                    <div class="bounty-amount">
                        <div style="font-size: 0.9em; opacity: 0.9;">预期获赔金额</div>
                        <div style="font-size: 1.8em; font-weight: bold; margin-top: 5px;">5万元</div>
                    </div>

                    <div class="commission-info">
                        <div>
                            <strong>律师分成：35%</strong><br>
                            <small>胜诉后支付</small>
                        </div>
                        <div>
                            <button class="btn btn-danger" onclick="applyBountyCase(3)">立即接案</button>
                        </div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-primary" onclick="alert('查看更多悬赏案件')">查看更多悬赏</button>
                <button class="btn btn-success" onclick="alert('发布悬赏委托')">发布悬赏委托</button>
            </div>
        </div>

        <!-- 行动号召 -->
        <div class="cta-section">
            <div class="container">
                <h2>开始您的法律维权之路</h2>
                <p>专业律师团队，为您提供全方位的法律服务</p>
                <div class="cta-buttons">
                    <a href="#" class="btn btn-success">免费咨询</a>
                    <a href="#" class="btn btn-primary">发布委托</a>
                    <a href="#" class="btn btn-danger">申请成为律师</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function applyBountyCase(caseId) {
            alert(`申请接案 ${caseId}：\n\n📋 申请流程\n1. 提交申请说明\n2. 展示相关经验\n3. 等待委托人确认\n4. 签署代理协议\n5. 开始案件代理\n\n💰 收费说明\n- 前期0费用\n- 胜诉后按比例分成\n- 败诉不收费\n\n⚖️ 专业要求\n- 必须是认证律师\n- 专业领域匹配\n- 具备相关经验`);
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.success-case-card, .bounty-case-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });
        });
    </script>
</body>
</html>
