{"name": "lawsuit-system-frontend", "version": "1.0.0", "description": "起诉书生成系统前端管理界面", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "axios": "^1.4.0", "@element-plus/icons-vue": "^2.1.0", "dayjs": "^1.11.9", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "quill": "^1.3.7", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "prettier": "^3.0.0", "@vue/eslint-config-prettier": "^8.0.0", "sass": "^1.64.1", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "unplugin-element-plus": "^0.8.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}