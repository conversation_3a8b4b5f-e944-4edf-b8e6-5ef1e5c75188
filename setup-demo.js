#!/usr/bin/env node

/**
 * 起诉书生成系统演示环境设置脚本
 * 
 * 使用方法：
 * 1. 确保已安装 Node.js 和 MySQL
 * 2. 运行: node setup-demo.js
 * 3. 按照提示完成设置
 */

const fs = require('fs');
const path = require('path');

console.log('🏛️  起诉书生成系统 - 演示环境设置');
console.log('=====================================\n');

// 检查必要文件
function checkFiles() {
    const requiredFiles = [
        'database/schema.sql',
        'database/sample-data.sql',
        'backend/package.json',
        'backend/.env.example'
    ];

    console.log('📋 检查必要文件...');
    
    for (const file of requiredFiles) {
        if (!fs.existsSync(file)) {
            console.error(`❌ 缺少文件: ${file}`);
            return false;
        }
        console.log(`✅ ${file}`);
    }
    
    return true;
}

// 创建目录结构
function createDirectories() {
    const directories = [
        'backend/logs',
        'backend/uploads',
        'backend/uploads/evidence',
        'backend/uploads/templates',
        'backend/uploads/avatars',
        'backend/temp',
        'frontend/dist'
    ];

    console.log('\n📁 创建目录结构...');
    
    for (const dir of directories) {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            console.log(`✅ 创建目录: ${dir}`);
        } else {
            console.log(`📁 目录已存在: ${dir}`);
        }
    }
}

// 生成环境配置文件
function generateEnvFile() {
    console.log('\n⚙️  生成环境配置文件...');
    
    const envContent = `# 起诉书生成系统环境配置
# 演示环境配置 - 请根据实际情况修改

# 服务器配置
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=lawsuit_system
DB_USER=root
DB_PASSWORD=your_password
DB_DIALECT=mysql

# JWT配置
JWT_SECRET=demo_jwt_secret_key_please_change_in_production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 文件上传配置
UPLOAD_PATH=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=起诉书生成系统

# Redis配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# 文档生成配置
DOCUMENT_RETENTION_DAYS=30
TEMP_DIR=temp

# 系统配置
SYSTEM_NAME=起诉书生成系统
SYSTEM_VERSION=1.0.0
ADMIN_EMAIL=<EMAIL>
`;

    fs.writeFileSync('backend/.env', envContent);
    console.log('✅ 生成 backend/.env 文件');
}

// 生成启动脚本
function generateStartScript() {
    console.log('\n🚀 生成启动脚本...');
    
    const startScript = `#!/bin/bash

# 起诉书生成系统启动脚本

echo "🏛️  启动起诉书生成系统..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 请先安装 Node.js"
    exit 1
fi

# 检查 MySQL
if ! command -v mysql &> /dev/null; then
    echo "❌ 请先安装 MySQL"
    exit 1
fi

# 进入后端目录
cd backend

# 安装依赖
echo "📦 安装后端依赖..."
npm install

# 启动后端服务
echo "🚀 启动后端服务..."
npm run dev &

# 等待服务启动
sleep 3

echo "✅ 系统启动完成！"
echo ""
echo "📱 访问地址："
echo "   - 演示系统: http://localhost:3000/demo-system.html"
echo "   - 简单表单: http://localhost:3000/index.html"
echo "   - 系统预览: http://localhost:3000/system-preview.html"
echo "   - 后端API: http://localhost:3001/api"
echo ""
echo "👤 演示账户："
echo "   - 管理员: admin / admin123"
echo "   - 律师: lawyer_zhang / lawyer123"
echo "   - 用户: user_wang / user123"
echo ""
echo "📖 使用说明："
echo "   1. 首先设置数据库连接信息"
echo "   2. 执行数据库初始化脚本"
echo "   3. 启动后端服务"
echo "   4. 访问演示页面"
`;

    fs.writeFileSync('start-demo.sh', startScript);
    console.log('✅ 生成 start-demo.sh 启动脚本');
    
    // Windows 批处理文件
    const startBat = `@echo off
echo 🏛️  启动起诉书生成系统...

REM 检查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 请先安装 Node.js
    pause
    exit /b 1
)

REM 进入后端目录
cd backend

REM 安装依赖
echo 📦 安装后端依赖...
npm install

REM 启动后端服务
echo 🚀 启动后端服务...
start npm run dev

REM 等待服务启动
timeout /t 3 /nobreak >nul

echo ✅ 系统启动完成！
echo.
echo 📱 访问地址：
echo    - 演示系统: http://localhost:3000/demo-system.html
echo    - 简单表单: http://localhost:3000/index.html
echo    - 系统预览: http://localhost:3000/system-preview.html
echo    - 后端API: http://localhost:3001/api
echo.
echo 👤 演示账户：
echo    - 管理员: admin / admin123
echo    - 律师: lawyer_zhang / lawyer123
echo    - 用户: user_wang / user123
echo.
pause
`;

    fs.writeFileSync('start-demo.bat', startBat);
    console.log('✅ 生成 start-demo.bat 启动脚本');
}

// 生成数据库初始化脚本
function generateDbScript() {
    console.log('\n🗄️  生成数据库初始化脚本...');
    
    const dbScript = `#!/bin/bash

# 数据库初始化脚本

echo "🗄️  初始化数据库..."

# 检查 MySQL
if ! command -v mysql &> /dev/null; then
    echo "❌ 请先安装 MySQL"
    exit 1
fi

# 提示用户输入数据库信息
read -p "请输入 MySQL 用户名 (默认: root): " DB_USER
DB_USER=\${DB_USER:-root}

read -s -p "请输入 MySQL 密码: " DB_PASSWORD
echo

read -p "请输入数据库主机 (默认: localhost): " DB_HOST
DB_HOST=\${DB_HOST:-localhost}

read -p "请输入数据库端口 (默认: 3306): " DB_PORT
DB_PORT=\${DB_PORT:-3306}

# 测试数据库连接
echo "🔗 测试数据库连接..."
mysql -h\$DB_HOST -P\$DB_PORT -u\$DB_USER -p\$DB_PASSWORD -e "SELECT 1;" > /dev/null 2>&1

if [ \$? -ne 0 ]; then
    echo "❌ 数据库连接失败，请检查连接信息"
    exit 1
fi

echo "✅ 数据库连接成功"

# 执行数据库脚本
echo "📋 创建数据库结构..."
mysql -h\$DB_HOST -P\$DB_PORT -u\$DB_USER -p\$DB_PASSWORD < database/schema.sql

if [ \$? -eq 0 ]; then
    echo "✅ 数据库结构创建成功"
else
    echo "❌ 数据库结构创建失败"
    exit 1
fi

echo "📊 插入示例数据..."
mysql -h\$DB_HOST -P\$DB_PORT -u\$DB_USER -p\$DB_PASSWORD < database/sample-data.sql

if [ \$? -eq 0 ]; then
    echo "✅ 示例数据插入成功"
else
    echo "❌ 示例数据插入失败"
    exit 1
fi

echo "🎉 数据库初始化完成！"
echo ""
echo "📊 数据库信息："
echo "   - 数据库名: lawsuit_system"
echo "   - 用户数: 6"
echo "   - 案件数: 3"
echo "   - 模板数: 3"
echo ""
echo "👤 默认账户："
echo "   - 管理员: <EMAIL> / admin123"
echo "   - 律师: <EMAIL> / lawyer123"
echo "   - 用户: <EMAIL> / user123"
`;

    fs.writeFileSync('init-database.sh', dbScript);
    console.log('✅ 生成 init-database.sh 数据库初始化脚本');
}

// 生成说明文档
function generateReadme() {
    console.log('\n📖 生成说明文档...');
    
    const readmeContent = `# 起诉书生成系统 - 演示环境

## 🎯 快速开始

### 1. 环境要求
- Node.js 16+
- MySQL 8.0+
- 现代浏览器

### 2. 安装步骤

#### 方式一：自动安装（推荐）
\`\`\`bash
# 运行设置脚本
node setup-demo.js

# 初始化数据库
./init-database.sh  # Linux/Mac
# 或
init-database.bat   # Windows

# 启动系统
./start-demo.sh     # Linux/Mac
# 或
start-demo.bat      # Windows
\`\`\`

#### 方式二：手动安装
\`\`\`bash
# 1. 安装后端依赖
cd backend
npm install

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置数据库连接信息

# 3. 初始化数据库
mysql -u root -p < ../database/schema.sql
mysql -u root -p < ../database/sample-data.sql

# 4. 启动后端服务
npm run dev
\`\`\`

### 3. 访问系统

- **完整演示**: http://localhost:3000/demo-system.html
- **简单表单**: http://localhost:3000/index.html
- **系统预览**: http://localhost:3000/system-preview.html
- **API文档**: http://localhost:3001/api

### 4. 演示账户

| 角色 | 用户名 | 密码 | 邮箱 |
|------|--------|------|------|
| 管理员 | admin | admin123 | <EMAIL> |
| 律师 | lawyer_zhang | lawyer123 | <EMAIL> |
| 用户 | user_wang | user123 | <EMAIL> |

## 🎮 功能演示

### 用户管理
- ✅ 用户注册/登录
- ✅ 角色权限管理
- ✅ 用户信息管理

### 案件管理
- ✅ 案件创建/编辑
- ✅ 案件分类管理
- ✅ 案件状态跟踪
- ✅ 案件搜索/筛选

### 模板管理
- ✅ 模板创建/编辑
- ✅ 模板版本控制
- ✅ 模板预览
- ✅ 变量替换

### 文档生成
- ✅ 动态表单生成
- ✅ 文档实时预览
- ✅ 多格式导出
- ✅ 批量生成

### 系统管理
- ✅ 系统配置
- ✅ 操作日志
- ✅ 数据统计

## 📊 演示数据

系统包含以下演示数据：

- **6个用户**: 1个管理员，2个律师，3个普通用户
- **3个案件**: 合同纠纷、侵权责任、劳动争议
- **5种案件类型**: 涵盖民事、劳动、家庭等
- **3个模板**: 专业的起诉书模板
- **完整日志**: 用户操作记录

## 🔧 技术架构

### 后端
- **框架**: Node.js + Express
- **数据库**: MySQL + Sequelize ORM
- **认证**: JWT Token
- **日志**: Winston
- **文件处理**: Multer

### 前端
- **基础版**: HTML + CSS + JavaScript
- **管理版**: Vue.js 3 + Element Plus (计划中)

### 文档生成
- **Word**: docx 库
- **PDF**: html-pdf 库
- **模板**: Handlebars

## 📝 API接口

### 认证相关
- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录
- GET /api/auth/profile - 获取用户信息

### 案件管理
- GET /api/cases - 获取案件列表
- POST /api/cases - 创建案件
- GET /api/cases/:id - 获取案件详情
- PUT /api/cases/:id - 更新案件

### 模板管理
- GET /api/templates - 获取模板列表
- POST /api/templates - 创建模板
- PUT /api/templates/:id - 更新模板

### 文档生成
- POST /api/documents/generate - 生成文档
- GET /api/documents/:id/download - 下载文档

## 🚀 部署说明

### 开发环境
\`\`\`bash
npm run dev
\`\`\`

### 生产环境
\`\`\`bash
npm run build
npm start
\`\`\`

### Docker部署
\`\`\`bash
docker-compose up -d
\`\`\`

## 📞 技术支持

如有问题，请联系：
- 邮箱: <EMAIL>
- 电话: 400-123-4567

## 📄 许可证

MIT License
`;

    fs.writeFileSync('DEMO-README.md', readmeContent);
    console.log('✅ 生成 DEMO-README.md 说明文档');
}

// 主函数
function main() {
    try {
        // 检查文件
        if (!checkFiles()) {
            console.error('\n❌ 文件检查失败，请确保所有必要文件存在');
            process.exit(1);
        }

        // 创建目录
        createDirectories();

        // 生成配置文件
        generateEnvFile();

        // 生成脚本
        generateStartScript();
        generateDbScript();

        // 生成文档
        generateReadme();

        console.log('\n🎉 演示环境设置完成！');
        console.log('\n📋 下一步操作：');
        console.log('1. 编辑 backend/.env 文件，设置数据库连接信息');
        console.log('2. 运行 ./init-database.sh 初始化数据库');
        console.log('3. 运行 ./start-demo.sh 启动系统');
        console.log('4. 访问 http://localhost:3000/demo-system.html');
        console.log('\n📖 详细说明请查看 DEMO-README.md');

    } catch (error) {
        console.error('\n❌ 设置过程中出现错误:', error.message);
        process.exit(1);
    }
}

// 运行主函数
main();
