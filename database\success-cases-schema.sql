-- 胜诉案例和悬赏委托功能扩展

USE lawsuit_system;

-- 胜诉案例表
CREATE TABLE success_cases (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lawyer_id INT NOT NULL COMMENT '律师ID',
    case_id INT COMMENT '关联案件ID',
    title VARCHAR(200) NOT NULL COMMENT '案例标题',
    case_type VARCHAR(100) NOT NULL COMMENT '案件类型',
    plaintiff_description VARCHAR(200) COMMENT '原告描述(脱敏)',
    defendant_description VARCHAR(200) COMMENT '被告描述(脱敏)',
    dispute_reason TEXT NOT NULL COMMENT '争议原因',
    claim_amount DECIMAL(15,2) NOT NULL COMMENT '索赔金额',
    awarded_amount DECIMAL(15,2) NOT NULL COMMENT '获赔金额',
    case_duration INT COMMENT '案件周期(天)',
    court_level ENUM('basic', 'intermediate', 'high', 'supreme') COMMENT '审理法院级别',
    case_summary TEXT COMMENT '案例摘要',
    legal_points TEXT COMMENT '法律要点',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否精选案例',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开展示',
    verified_at TIMESTAMP NULL COMMENT '认证时间',
    verified_by INT COMMENT '认证管理员',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (lawyer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE SET NULL,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_lawyer_id (lawyer_id),
    INDEX idx_case_type (case_type),
    INDEX idx_featured (is_featured),
    INDEX idx_public (is_public),
    INDEX idx_awarded_amount (awarded_amount)
) COMMENT '胜诉案例表';

-- 悬赏委托表
CREATE TABLE bounty_cases (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '委托用户ID',
    case_type_id INT NOT NULL COMMENT '案件类型ID',
    title VARCHAR(200) NOT NULL COMMENT '委托标题',
    description TEXT NOT NULL COMMENT '案件描述',
    dispute_amount DECIMAL(15,2) NOT NULL COMMENT '争议金额',
    expected_compensation DECIMAL(15,2) NOT NULL COMMENT '预期赔偿金额',
    commission_rate DECIMAL(5,2) NOT NULL COMMENT '律师费分成比例(%)',
    min_lawyer_level ENUM('junior', 'intermediate', 'senior', 'expert') DEFAULT 'junior' COMMENT '最低律师等级要求',
    required_specialty_id INT COMMENT '要求专业ID',
    case_urgency ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '案件紧急程度',
    case_complexity ENUM('simple', 'medium', 'complex', 'very_complex') DEFAULT 'medium' COMMENT '案件复杂程度',
    evidence_strength ENUM('weak', 'medium', 'strong', 'very_strong') DEFAULT 'medium' COMMENT '证据强度',
    location VARCHAR(100) COMMENT '案件所在地',
    deadline DATE COMMENT '期望完成时间',
    additional_requirements TEXT COMMENT '其他要求',
    status ENUM('open', 'assigned', 'in_progress', 'completed', 'cancelled', 'failed') DEFAULT 'open' COMMENT '状态',
    assigned_lawyer_id INT COMMENT '接案律师ID',
    assigned_at TIMESTAMP NULL COMMENT '接案时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    final_compensation DECIMAL(15,2) COMMENT '最终获赔金额',
    lawyer_fee DECIMAL(15,2) COMMENT '律师费用',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    application_count INT DEFAULT 0 COMMENT '申请次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (case_type_id) REFERENCES case_types(id) ON DELETE RESTRICT,
    FOREIGN KEY (required_specialty_id) REFERENCES lawyer_specialties(id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_lawyer_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_case_type (case_type_id),
    INDEX idx_status (status),
    INDEX idx_specialty (required_specialty_id),
    INDEX idx_assigned_lawyer (assigned_lawyer_id),
    INDEX idx_featured (is_featured),
    INDEX idx_created_at (created_at)
) COMMENT '悬赏委托表';

-- 悬赏申请表
CREATE TABLE bounty_applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bounty_case_id INT NOT NULL COMMENT '悬赏案件ID',
    lawyer_id INT NOT NULL COMMENT '申请律师ID',
    application_message TEXT COMMENT '申请说明',
    proposed_strategy TEXT COMMENT '拟定策略',
    estimated_duration INT COMMENT '预估周期(天)',
    success_probability DECIMAL(5,2) COMMENT '胜诉概率(%)',
    similar_cases_count INT DEFAULT 0 COMMENT '类似案件经验',
    commission_rate DECIMAL(5,2) COMMENT '期望分成比例(%)',
    status ENUM('pending', 'accepted', 'rejected', 'withdrawn') DEFAULT 'pending' COMMENT '申请状态',
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP NULL COMMENT '回复时间',
    response_message TEXT COMMENT '回复说明',
    FOREIGN KEY (bounty_case_id) REFERENCES bounty_cases(id) ON DELETE CASCADE,
    FOREIGN KEY (lawyer_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_bounty_lawyer (bounty_case_id, lawyer_id),
    INDEX idx_bounty_case (bounty_case_id),
    INDEX idx_lawyer_id (lawyer_id),
    INDEX idx_status (status),
    INDEX idx_applied_at (applied_at)
) COMMENT '悬赏申请表';

-- 律师收费标准表
CREATE TABLE lawyer_pricing (
    id INT PRIMARY KEY AUTO_INCREMENT,
    lawyer_id INT NOT NULL COMMENT '律师ID',
    hourly_rate DECIMAL(10,2) NOT NULL COMMENT '小时费率(元)',
    consultation_fee DECIMAL(10,2) COMMENT '咨询费(元)',
    document_fee DECIMAL(10,2) COMMENT '文书费(元)',
    court_appearance_fee DECIMAL(10,2) COMMENT '出庭费(元)',
    bounty_commission_rate DECIMAL(5,2) DEFAULT 30.00 COMMENT '悬赏案件分成比例(%)',
    min_case_amount DECIMAL(15,2) COMMENT '最低接案金额',
    payment_terms TEXT COMMENT '付费条款',
    discount_policy TEXT COMMENT '优惠政策',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    effective_date DATE NOT NULL COMMENT '生效日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (lawyer_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_lawyer_pricing (lawyer_id),
    INDEX idx_lawyer_id (lawyer_id),
    INDEX idx_hourly_rate (hourly_rate),
    INDEX idx_bounty_rate (bounty_commission_rate)
) COMMENT '律师收费标准表';

-- 案例评价表
CREATE TABLE case_reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    success_case_id INT COMMENT '胜诉案例ID',
    bounty_case_id INT COMMENT '悬赏案件ID',
    user_id INT NOT NULL COMMENT '评价用户ID',
    lawyer_id INT NOT NULL COMMENT '被评价律师ID',
    rating INT NOT NULL COMMENT '评分(1-5)',
    service_rating INT COMMENT '服务态度评分',
    professional_rating INT COMMENT '专业能力评分',
    efficiency_rating INT COMMENT '办事效率评分',
    communication_rating INT COMMENT '沟通能力评分',
    review_content TEXT COMMENT '评价内容',
    is_anonymous BOOLEAN DEFAULT FALSE COMMENT '是否匿名',
    is_public BOOLEAN DEFAULT TRUE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (success_case_id) REFERENCES success_cases(id) ON DELETE CASCADE,
    FOREIGN KEY (bounty_case_id) REFERENCES bounty_cases(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (lawyer_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_success_case (success_case_id),
    INDEX idx_bounty_case (bounty_case_id),
    INDEX idx_lawyer_id (lawyer_id),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at)
) COMMENT '案例评价表';

-- 插入示例胜诉案例数据
INSERT INTO success_cases (lawyer_id, title, case_type, plaintiff_description, defendant_description, dispute_reason, claim_amount, awarded_amount, case_duration, court_level, case_summary, legal_points, is_featured, verified_at, verified_by) VALUES
(2, '装修合同纠纷胜诉案例', '合同纠纷', '某业主', '某装修公司', '装修质量严重不符合合同约定，存在多处质量问题', 120000.00, 95000.00, 45, 'basic', '成功为业主追回装修费用并获得额外赔偿，通过专业的质量鉴定和法律论证，证明装修公司存在严重违约行为', '合同法第577条、第584条的适用，质量标准的认定，违约责任的承担', TRUE, NOW(), 1),

(2, '买卖合同违约赔偿案', '合同纠纷', '某采购商', '某供应商', '供应商延期交货并提供不合格产品，造成重大经济损失', 500000.00, 380000.00, 60, 'intermediate', '通过详实的证据收集和精准的法律适用，成功为客户挽回大部分经济损失', '合同法违约责任、可得利益损失的计算、减损义务的履行', TRUE, NOW(), 1),

(3, '劳动争议仲裁胜诉', '劳动合同纠纷', '某技术员工', '某科技公司', '公司违法解除劳动合同，拒付加班费和经济补偿', 85000.00, 78000.00, 30, 'basic', '通过劳动仲裁程序，成功为员工争取到违法解除赔偿金和拖欠的加班费', '劳动合同法第48条、第87条的适用，加班费计算标准', TRUE, NOW(), 1),

(3, '工伤赔偿争议案', '劳动合同纠纷', '某工人', '某建筑公司', '工伤认定后公司拒绝承担相应赔偿责任', 150000.00, 142000.00, 90, 'basic', '协助客户完成工伤认定程序，并通过法律途径获得全额工伤赔偿', '工伤保险条例、劳动能力鉴定标准、赔偿项目计算', FALSE, NOW(), 1);

-- 插入示例悬赏委托数据
INSERT INTO bounty_cases (user_id, case_type_id, title, description, dispute_amount, expected_compensation, commission_rate, required_specialty_id, case_urgency, case_complexity, evidence_strength, location, deadline, status, is_featured) VALUES
(4, 1, '商铺租赁合同纠纷求助', '房东违约提前收回商铺，造成装修损失和营业损失，需要专业律师代理追讨赔偿', 200000.00, 180000.00, 25.00, 1, 'high', 'medium', 'strong', '北京市朝阳区', '2024-06-30', 'open', TRUE),

(5, 2, '网络诽谤名誉权维权', '某自媒体恶意传播不实信息，严重损害个人名誉和商业信誉，需要律师协助维权', 100000.00, 80000.00, 30.00, 2, 'medium', 'medium', 'medium', '上海市浦东新区', '2024-05-15', 'open', TRUE),

(4, 3, '劳动仲裁代理需求', '公司恶意调岗降薪，变相逼迫离职，需要专业劳动法律师代理仲裁', 60000.00, 50000.00, 35.00, 3, 'medium', 'simple', 'strong', '深圳市南山区', '2024-04-20', 'open', FALSE);

-- 插入律师收费标准数据
INSERT INTO lawyer_pricing (lawyer_id, hourly_rate, consultation_fee, document_fee, court_appearance_fee, bounty_commission_rate, min_case_amount, payment_terms, discount_policy, effective_date) VALUES
(2, 800.00, 200.00, 1500.00, 3000.00, 25.00, 10000.00, '咨询费当场支付，代理费分期支付，胜诉后结清', '连续委托案件享受9折优惠，困难群体可申请法律援助', '2024-01-01'),

(3, 600.00, 150.00, 1200.00, 2500.00, 30.00, 5000.00, '首次咨询免费，代理费可分2-3期支付', '学生、老人、残疾人享受8折优惠', '2024-01-01');

-- 插入案例评价数据
INSERT INTO case_reviews (success_case_id, user_id, lawyer_id, rating, service_rating, professional_rating, efficiency_rating, communication_rating, review_content, is_public) VALUES
(1, 4, 2, 5, 5, 5, 4, 5, '张律师非常专业，对案件分析透彻，最终帮我成功维权，非常感谢！', TRUE),
(2, 5, 2, 4, 4, 5, 4, 4, '专业能力很强，案件处理得很好，就是沟通频率可以再高一些', TRUE),
(3, 4, 3, 5, 5, 4, 5, 5, '李律师对劳动法非常熟悉，效率很高，一个月就解决了问题', TRUE);

-- 创建视图：首页胜诉案例展示
CREATE VIEW homepage_success_cases AS
SELECT 
    sc.id,
    sc.title,
    sc.case_type,
    sc.plaintiff_description,
    sc.defendant_description,
    sc.dispute_reason,
    sc.claim_amount,
    sc.awarded_amount,
    sc.case_duration,
    sc.court_level,
    sc.case_summary,
    u.real_name as lawyer_name,
    lp.law_firm,
    lp.practice_years,
    lp.success_rate,
    AVG(cr.rating) as avg_rating,
    COUNT(cr.id) as review_count
FROM success_cases sc
JOIN users u ON sc.lawyer_id = u.id
JOIN lawyer_profiles lp ON u.id = lp.user_id
LEFT JOIN case_reviews cr ON sc.id = cr.success_case_id AND cr.is_public = TRUE
WHERE sc.is_public = TRUE AND sc.verified_at IS NOT NULL
GROUP BY sc.id
ORDER BY sc.is_featured DESC, sc.awarded_amount DESC, sc.created_at DESC;

-- 创建视图：悬赏案件展示
CREATE VIEW homepage_bounty_cases AS
SELECT 
    bc.id,
    bc.title,
    bc.description,
    bc.dispute_amount,
    bc.expected_compensation,
    bc.commission_rate,
    bc.case_urgency,
    bc.case_complexity,
    bc.evidence_strength,
    bc.location,
    bc.deadline,
    bc.view_count,
    bc.application_count,
    bc.created_at,
    ct.name as case_type_name,
    ls.name as required_specialty_name,
    u.real_name as user_name
FROM bounty_cases bc
JOIN case_types ct ON bc.case_type_id = ct.id
LEFT JOIN lawyer_specialties ls ON bc.required_specialty_id = ls.id
JOIN users u ON bc.user_id = u.id
WHERE bc.status = 'open'
ORDER BY bc.is_featured DESC, bc.expected_compensation DESC, bc.created_at DESC;
