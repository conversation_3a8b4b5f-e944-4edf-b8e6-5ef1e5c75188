# 起诉书生成系统架构设计

## 系统概述

这是一个前后端分离的起诉书生成系统，支持多种案件类型，具备用户管理、模板管理、文档生成等完整功能。

## 技术栈

### 后端
- **框架**: Node.js + Express
- **数据库**: MySQL 8.0
- **ORM**: Sequelize
- **认证**: JWT
- **文档生成**: docx, html-pdf
- **文件存储**: 本地存储 + 云存储(可选)

### 前端
- **框架**: Vue.js 3 + Vite
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **富文本编辑**: Quill.js

### 部署
- **容器化**: Docker
- **反向代理**: Nginx
- **进程管理**: PM2

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端管理系统   │    │   用户前台系统   │    │   移动端应用     │
│   (Vue.js)      │    │   (Vue.js)      │    │   (响应式)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (Express)     │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户服务       │    │   案件服务       │    │   文档服务       │
│   (User API)    │    │   (Case API)    │    │   (Doc API)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL数据库    │
                    └─────────────────┘
```

## 数据库设计

### 用户表 (users)
- id (主键)
- username (用户名)
- email (邮箱)
- password (密码哈希)
- role (角色: admin/lawyer/user)
- status (状态: active/inactive)
- created_at, updated_at

### 案件类型表 (case_types)
- id (主键)
- name (类型名称)
- code (类型代码)
- description (描述)
- template_fields (模板字段JSON)
- is_active (是否启用)

### 案件表 (cases)
- id (主键)
- user_id (用户ID)
- case_type_id (案件类型ID)
- title (案件标题)
- case_data (案件数据JSON)
- status (状态: draft/completed/archived)
- created_at, updated_at

### 文档模板表 (templates)
- id (主键)
- case_type_id (案件类型ID)
- name (模板名称)
- content (模板内容)
- variables (变量定义JSON)
- version (版本号)
- is_default (是否默认模板)

### 生成记录表 (documents)
- id (主键)
- case_id (案件ID)
- template_id (模板ID)
- file_path (文件路径)
- file_type (文件类型: docx/pdf)
- generated_at (生成时间)

## API接口设计

### 认证相关
- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录
- POST /api/auth/logout - 用户登出
- GET /api/auth/profile - 获取用户信息

### 用户管理
- GET /api/users - 获取用户列表
- GET /api/users/:id - 获取用户详情
- PUT /api/users/:id - 更新用户信息
- DELETE /api/users/:id - 删除用户

### 案件类型管理
- GET /api/case-types - 获取案件类型列表
- POST /api/case-types - 创建案件类型
- PUT /api/case-types/:id - 更新案件类型
- DELETE /api/case-types/:id - 删除案件类型

### 案件管理
- GET /api/cases - 获取案件列表
- POST /api/cases - 创建案件
- GET /api/cases/:id - 获取案件详情
- PUT /api/cases/:id - 更新案件
- DELETE /api/cases/:id - 删除案件

### 模板管理
- GET /api/templates - 获取模板列表
- POST /api/templates - 创建模板
- PUT /api/templates/:id - 更新模板
- DELETE /api/templates/:id - 删除模板

### 文档生成
- POST /api/documents/generate - 生成文档
- GET /api/documents/:id/download - 下载文档
- GET /api/documents/:id/preview - 预览文档

## 功能模块

### 1. 用户管理模块
- 用户注册/登录
- 角色权限管理
- 用户信息管理

### 2. 案件管理模块
- 案件创建/编辑
- 案件分类管理
- 案件状态跟踪

### 3. 模板管理模块
- 模板创建/编辑
- 模板版本控制
- 模板预览

### 4. 文档生成模块
- 动态表单生成
- 文档实时预览
- 多格式导出

### 5. 系统管理模块
- 系统配置
- 日志管理
- 数据备份

## 支持的案件类型

1. **民事案件**
   - 合同纠纷
   - 侵权责任纠纷
   - 物权纠纷
   - 债权债务纠纷

2. **劳动案件**
   - 劳动合同纠纷
   - 工伤赔偿
   - 劳动报酬纠纷

3. **婚姻家庭案件**
   - 离婚纠纷
   - 财产分割
   - 子女抚养

4. **知识产权案件**
   - 著作权纠纷
   - 商标权纠纷
   - 专利权纠纷

5. **行政案件**
   - 行政诉讼
   - 行政复议

## 安全设计

### 认证授权
- JWT Token认证
- 角色基础访问控制(RBAC)
- API接口权限验证

### 数据安全
- 密码加密存储
- 敏感数据加密
- SQL注入防护
- XSS攻击防护

### 文件安全
- 文件类型验证
- 文件大小限制
- 安全的文件存储

## 部署架构

```
Internet
    │
┌───▼───┐
│ Nginx │ (反向代理/负载均衡)
└───┬───┘
    │
┌───▼───┐
│ Node.js│ (API服务器)
│ + PM2  │
└───┬───┘
    │
┌───▼───┐
│ MySQL │ (数据库)
└───────┘
```

## 开发计划

1. **第一阶段**: 基础架构搭建
2. **第二阶段**: 核心功能开发
3. **第三阶段**: 高级功能实现
4. **第四阶段**: 测试和优化
5. **第五阶段**: 部署和上线
