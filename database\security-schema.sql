-- 用户数据安全性和律师专业化管理扩展

USE lawsuit_system;

-- 律师专业表
CREATE TABLE lawyer_specialties (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '专业名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '专业代码',
    description TEXT COMMENT '专业描述',
    category ENUM('civil', 'labor', 'family', 'intellectual', 'administrative', 'criminal') NOT NULL COMMENT '专业分类',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_category (category)
) COMMENT '律师专业表';

-- 律师资料表
CREATE TABLE lawyer_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    specialty_id INT NOT NULL COMMENT '专业ID',
    license_number VARCHAR(100) UNIQUE NOT NULL COMMENT '律师执业证号',
    law_firm VARCHAR(200) COMMENT '所属律师事务所',
    practice_years INT DEFAULT 0 COMMENT '执业年限',
    case_count INT DEFAULT 0 COMMENT '代理案件数量',
    success_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '胜诉率',
    education TEXT COMMENT '教育背景',
    achievements TEXT COMMENT '专业成就',
    introduction TEXT COMMENT '个人介绍',
    hourly_rate DECIMAL(10,2) COMMENT '咨询费用(元/小时)',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否认证',
    verification_date TIMESTAMP NULL COMMENT '认证时间',
    verified_by INT COMMENT '认证管理员ID',
    status ENUM('pending', 'verified', 'rejected', 'suspended') DEFAULT 'pending' COMMENT '认证状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (specialty_id) REFERENCES lawyer_specialties(id) ON DELETE RESTRICT,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_specialty (user_id),
    INDEX idx_specialty (specialty_id),
    INDEX idx_license (license_number),
    INDEX idx_verified (is_verified),
    INDEX idx_status (status)
) COMMENT '律师资料表';

-- 用户律师授权表
CREATE TABLE user_lawyer_authorizations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    lawyer_id INT NOT NULL COMMENT '律师ID',
    case_id INT COMMENT '特定案件ID(NULL表示全部案件)',
    authorization_type ENUM('full', 'limited', 'view_only') DEFAULT 'limited' COMMENT '授权类型',
    permissions JSON COMMENT '具体权限配置',
    status ENUM('pending', 'active', 'expired', 'revoked') DEFAULT 'pending' COMMENT '授权状态',
    authorized_at TIMESTAMP NULL COMMENT '授权时间',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    revoked_at TIMESTAMP NULL COMMENT '撤销时间',
    revoked_by INT COMMENT '撤销人ID',
    notes TEXT COMMENT '授权备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (lawyer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE CASCADE,
    FOREIGN KEY (revoked_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_lawyer_case (user_id, lawyer_id, case_id),
    INDEX idx_user_id (user_id),
    INDEX idx_lawyer_id (lawyer_id),
    INDEX idx_case_id (case_id),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
) COMMENT '用户律师授权表';

-- 数据访问日志表
CREATE TABLE data_access_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '访问用户ID',
    target_user_id INT COMMENT '被访问用户ID',
    resource_type ENUM('case', 'user_info', 'contact', 'document') NOT NULL COMMENT '资源类型',
    resource_id INT COMMENT '资源ID',
    access_type ENUM('view', 'edit', 'download', 'export') NOT NULL COMMENT '访问类型',
    access_level ENUM('full', 'summary', 'limited') NOT NULL COMMENT '访问级别',
    authorization_id INT COMMENT '授权记录ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    access_result ENUM('success', 'denied', 'error') DEFAULT 'success' COMMENT '访问结果',
    denial_reason VARCHAR(255) COMMENT '拒绝原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (target_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (authorization_id) REFERENCES user_lawyer_authorizations(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_target_user (target_user_id),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created_at (created_at)
) COMMENT '数据访问日志表';

-- 敏感数据加密表
CREATE TABLE encrypted_user_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    data_type ENUM('phone', 'id_number', 'address', 'bank_account') NOT NULL COMMENT '数据类型',
    encrypted_data TEXT NOT NULL COMMENT '加密数据',
    encryption_key_id VARCHAR(100) NOT NULL COMMENT '加密密钥ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_data_type (user_id, data_type),
    INDEX idx_user_id (user_id),
    INDEX idx_data_type (data_type)
) COMMENT '敏感数据加密表';

-- 律师案件专业匹配表
CREATE TABLE case_specialty_matches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    case_id INT NOT NULL COMMENT '案件ID',
    specialty_id INT NOT NULL COMMENT '专业ID',
    match_score DECIMAL(3,2) DEFAULT 1.00 COMMENT '匹配度(0-1)',
    is_primary BOOLEAN DEFAULT TRUE COMMENT '是否主要专业',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE CASCADE,
    FOREIGN KEY (specialty_id) REFERENCES lawyer_specialties(id) ON DELETE CASCADE,
    UNIQUE KEY unique_case_specialty (case_id, specialty_id),
    INDEX idx_case_id (case_id),
    INDEX idx_specialty_id (specialty_id)
) COMMENT '案件专业匹配表';

-- 插入律师专业数据
INSERT INTO lawyer_specialties (name, code, description, category, is_active) VALUES
('合同法律师', 'CONTRACT_LAW', '专门处理各类合同纠纷，包括买卖合同、服务合同、租赁合同等', 'civil', TRUE),
('侵权法律师', 'TORT_LAW', '专门处理侵权责任纠纷，包括人身损害、财产损害、名誉权等', 'civil', TRUE),
('劳动法律师', 'LABOR_LAW', '专门处理劳动争议，包括劳动合同、工伤赔偿、社保纠纷等', 'labor', TRUE),
('婚姻家庭律师', 'FAMILY_LAW', '专门处理婚姻家庭纠纷，包括离婚、财产分割、子女抚养等', 'family', TRUE),
('知识产权律师', 'IP_LAW', '专门处理知识产权纠纷，包括专利、商标、著作权等', 'intellectual', TRUE),
('行政法律师', 'ADMIN_LAW', '专门处理行政诉讼，包括行政复议、行政赔偿等', 'administrative', TRUE),
('刑事辩护律师', 'CRIMINAL_LAW', '专门处理刑事案件辩护', 'criminal', TRUE);

-- 更新案件类型与专业的关联
UPDATE case_types SET template_fields = JSON_SET(template_fields, '$.required_specialty', 'CONTRACT_LAW') WHERE code = 'CONTRACT_DISPUTE';
UPDATE case_types SET template_fields = JSON_SET(template_fields, '$.required_specialty', 'TORT_LAW') WHERE code = 'TORT_LIABILITY';
UPDATE case_types SET template_fields = JSON_SET(template_fields, '$.required_specialty', 'LABOR_LAW') WHERE code = 'LABOR_CONTRACT';
UPDATE case_types SET template_fields = JSON_SET(template_fields, '$.required_specialty', 'FAMILY_LAW') WHERE code = 'DIVORCE';
UPDATE case_types SET template_fields = JSON_SET(template_fields, '$.required_specialty', 'CONTRACT_LAW') WHERE code = 'DEBT_DISPUTE';

-- 插入律师资料示例数据
INSERT INTO lawyer_profiles (user_id, specialty_id, license_number, law_firm, practice_years, case_count, success_rate, education, achievements, introduction, hourly_rate, is_verified, verification_date, verified_by, status) VALUES
(2, 1, '*********', '北京市某某律师事务所', 6, 156, 85.50, '北京大学法学院法学硕士', '连续三年被评为优秀合同法律师，成功代理多起重大合同纠纷案件', '专注合同法领域6年，擅长处理各类合同纠纷，具有丰富的诉讼和仲裁经验', 800.00, TRUE, NOW() - INTERVAL 30 DAY, 1, 'verified'),
(3, 3, '*********', '上海市某某律师事务所', 5, 89, 78.20, '华东政法大学法学学士', '劳动法专业律师，成功为多家企业和个人解决劳动争议', '专业处理劳动合同纠纷、工伤赔偿等案件，维护劳动者合法权益', 600.00, TRUE, NOW() - INTERVAL 20 DAY, 1, 'verified');

-- 插入案件专业匹配数据
INSERT INTO case_specialty_matches (case_id, specialty_id, match_score, is_primary) VALUES
(1, 1, 1.00, TRUE),  -- 合同纠纷案件匹配合同法专业
(2, 2, 1.00, TRUE),  -- 侵权案件匹配侵权法专业
(3, 3, 1.00, TRUE);  -- 劳动纠纷案件匹配劳动法专业

-- 插入用户授权示例数据
INSERT INTO user_lawyer_authorizations (user_id, lawyer_id, case_id, authorization_type, permissions, status, authorized_at, expires_at) VALUES
(4, 2, 1, 'full', '{"view_contact": true, "edit_case": true, "generate_document": true, "communicate": true}', 'active', NOW() - INTERVAL 2 DAY, NOW() + INTERVAL 90 DAY),
(4, 3, 3, 'limited', '{"view_contact": true, "edit_case": false, "generate_document": true, "communicate": true}', 'active', NOW() - INTERVAL 1 DAY, NOW() + INTERVAL 60 DAY);

-- 创建视图：律师可见案件摘要
CREATE VIEW lawyer_visible_cases AS
SELECT 
    c.id,
    c.case_number,
    c.title,
    c.status,
    c.priority,
    c.created_at,
    c.updated_at,
    ct.name as case_type_name,
    ct.category,
    -- 脱敏的当事人信息
    JSON_OBJECT(
        'name', CONCAT(LEFT(JSON_UNQUOTE(c.plaintiff_info->'$.name'), 1), '**'),
        'type', JSON_UNQUOTE(c.plaintiff_info->'$.type')
    ) as plaintiff_summary,
    JSON_OBJECT(
        'name', CONCAT(LEFT(JSON_UNQUOTE(c.defendant_info->'$.name'), 1), '**'),
        'type', JSON_UNQUOTE(c.defendant_info->'$.type')
    ) as defendant_summary,
    -- 案件摘要（不包含敏感信息）
    LEFT(c.facts_and_reasons, 200) as case_summary,
    csm.specialty_id,
    ls.name as required_specialty
FROM cases c
JOIN case_types ct ON c.case_type_id = ct.id
LEFT JOIN case_specialty_matches csm ON c.id = csm.case_id AND csm.is_primary = TRUE
LEFT JOIN lawyer_specialties ls ON csm.specialty_id = ls.id;

-- 创建函数：检查律师是否有权限访问案件
DELIMITER //
CREATE FUNCTION check_lawyer_case_access(
    p_lawyer_id INT,
    p_case_id INT,
    p_access_type ENUM('view', 'edit', 'full')
) RETURNS ENUM('denied', 'summary', 'limited', 'full')
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_user_id INT;
    DECLARE v_lawyer_specialty_id INT;
    DECLARE v_case_specialty_id INT;
    DECLARE v_authorization_type VARCHAR(20);
    DECLARE v_authorization_status VARCHAR(20);
    DECLARE v_is_admin BOOLEAN DEFAULT FALSE;
    
    -- 检查是否是管理员
    SELECT role = 'admin' INTO v_is_admin 
    FROM users WHERE id = p_lawyer_id;
    
    IF v_is_admin THEN
        RETURN 'full';
    END IF;
    
    -- 获取案件所属用户
    SELECT user_id INTO v_user_id FROM cases WHERE id = p_case_id;
    
    -- 获取律师专业
    SELECT specialty_id INTO v_lawyer_specialty_id 
    FROM lawyer_profiles 
    WHERE user_id = p_lawyer_id AND is_verified = TRUE;
    
    -- 获取案件所需专业
    SELECT specialty_id INTO v_case_specialty_id 
    FROM case_specialty_matches 
    WHERE case_id = p_case_id AND is_primary = TRUE;
    
    -- 检查专业匹配
    IF v_lawyer_specialty_id != v_case_specialty_id THEN
        RETURN 'denied';
    END IF;
    
    -- 检查用户授权
    SELECT authorization_type, status INTO v_authorization_type, v_authorization_status
    FROM user_lawyer_authorizations 
    WHERE user_id = v_user_id 
      AND lawyer_id = p_lawyer_id 
      AND (case_id = p_case_id OR case_id IS NULL)
      AND status = 'active'
      AND (expires_at IS NULL OR expires_at > NOW())
    ORDER BY case_id DESC
    LIMIT 1;
    
    IF v_authorization_status = 'active' THEN
        CASE v_authorization_type
            WHEN 'full' THEN RETURN 'full';
            WHEN 'limited' THEN RETURN 'limited';
            WHEN 'view_only' THEN RETURN 'summary';
            ELSE RETURN 'summary';
        END CASE;
    ELSE
        RETURN 'summary';
    END IF;
END //
DELIMITER ;

-- 创建触发器：记录数据访问日志
DELIMITER //
CREATE TRIGGER log_case_access 
AFTER SELECT ON cases
FOR EACH ROW
BEGIN
    -- 这里需要在应用层实现，因为MySQL触发器不支持SELECT操作的AFTER触发器
    -- 实际实现会在应用代码中记录访问日志
END //
DELIMITER ;
