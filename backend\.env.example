# 服务器配置
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=lawsuit_system
DB_USER=root
DB_PASSWORD=your_password
DB_DIALECT=mysql

# JWT配置
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 文件上传配置
UPLOAD_PATH=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=起诉书生成系统

# Redis配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# 文档生成配置
DOCUMENT_RETENTION_DAYS=30
TEMP_DIR=temp

# 系统配置
SYSTEM_NAME=起诉书生成系统
SYSTEM_VERSION=1.0.0
ADMIN_EMAIL=<EMAIL>
