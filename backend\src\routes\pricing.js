const express = require('express');
const { body, validationResult } = require('express-validator');
const { LawyerPricing, LawyerProfile, User } = require('../models');
const { auth, lawyerOrAdmin } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// 获取律师收费标准
router.get('/lawyer/:lawyerId', async (req, res) => {
  try {
    const { lawyerId } = req.params;

    const pricing = await LawyerPricing.findOne({
      where: { lawyerId, isActive: true },
      include: [
        {
          model: User,
          as: 'lawyer',
          attributes: ['id', 'realName'],
          include: [
            {
              model: LawyerProfile,
              as: 'lawyerProfile',
              attributes: ['lawFirm', 'practiceYears', 'caseCount', 'successRate']
            }
          ]
        }
      ]
    });

    if (!pricing) {
      return res.status(404).json({
        success: false,
        message: '律师收费标准未设置'
      });
    }

    res.json({
      success: true,
      data: { pricing }
    });
  } catch (error) {
    logger.error('获取律师收费标准失败:', error);
    res.status(500).json({
      success: false,
      message: '获取律师收费标准失败'
    });
  }
});

// 设置/更新律师收费标准
router.post('/my', auth, lawyerOrAdmin, [
  body('hourlyRate').isFloat({ min: 50, max: 10000 }).withMessage('小时费率50-10000元'),
  body('consultationFee').optional().isFloat({ min: 0, max: 5000 }).withMessage('咨询费0-5000元'),
  body('documentFee').optional().isFloat({ min: 0, max: 10000 }).withMessage('文书费0-10000元'),
  body('courtAppearanceFee').optional().isFloat({ min: 0, max: 20000 }).withMessage('出庭费0-20000元'),
  body('bountyCommissionRate').isFloat({ min: 5, max: 50 }).withMessage('悬赏分成比例5%-50%'),
  body('minCaseAmount').optional().isFloat({ min: 0 }).withMessage('最低接案金额必须大于等于0'),
  body('paymentTerms').optional().isLength({ max: 1000 }).withMessage('付费条款最多1000字符'),
  body('discountPolicy').optional().isLength({ max: 1000 }).withMessage('优惠政策最多1000字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const lawyerId = req.user.id;
    const {
      hourlyRate,
      consultationFee,
      documentFee,
      courtAppearanceFee,
      bountyCommissionRate,
      minCaseAmount,
      paymentTerms,
      discountPolicy
    } = req.body;

    // 检查律师资格
    const lawyerProfile = await LawyerProfile.findByUser(lawyerId);
    if (!lawyerProfile || !lawyerProfile.isVerified) {
      return res.status(403).json({
        success: false,
        message: '您不是认证律师，无法设置收费标准'
      });
    }

    // 查找现有收费标准
    let pricing = await LawyerPricing.findOne({
      where: { lawyerId }
    });

    const pricingData = {
      lawyerId,
      hourlyRate,
      consultationFee: consultationFee || 0,
      documentFee: documentFee || 0,
      courtAppearanceFee: courtAppearanceFee || 0,
      bountyCommissionRate,
      minCaseAmount: minCaseAmount || 0,
      paymentTerms: paymentTerms || '',
      discountPolicy: discountPolicy || '',
      isActive: true,
      effectiveDate: new Date()
    };

    if (pricing) {
      // 更新现有收费标准
      await pricing.update(pricingData);
      logger.info(`律师更新收费标准`, { lawyerId, hourlyRate, bountyCommissionRate });
    } else {
      // 创建新的收费标准
      pricing = await LawyerPricing.create(pricingData);
      logger.info(`律师设置收费标准`, { lawyerId, hourlyRate, bountyCommissionRate });
    }

    res.json({
      success: true,
      message: '收费标准设置成功',
      data: { pricing }
    });
  } catch (error) {
    logger.error('设置律师收费标准失败:', error);
    res.status(500).json({
      success: false,
      message: '设置收费标准失败'
    });
  }
});

// 获取我的收费标准
router.get('/my', auth, lawyerOrAdmin, async (req, res) => {
  try {
    const lawyerId = req.user.id;

    const pricing = await LawyerPricing.findOne({
      where: { lawyerId }
    });

    if (!pricing) {
      return res.status(404).json({
        success: false,
        message: '您还未设置收费标准'
      });
    }

    res.json({
      success: true,
      data: { pricing }
    });
  } catch (error) {
    logger.error('获取我的收费标准失败:', error);
    res.status(500).json({
      success: false,
      message: '获取收费标准失败'
    });
  }
});

// 获取律师收费标准列表（用于比较）
router.get('/compare', async (req, res) => {
  try {
    const { specialtyId, location } = req.query;

    const whereClause = { isActive: true };
    const includeClause = [
      {
        model: User,
        as: 'lawyer',
        attributes: ['id', 'realName'],
        include: [
          {
            model: LawyerProfile,
            as: 'lawyerProfile',
            attributes: ['lawFirm', 'practiceYears', 'caseCount', 'successRate', 'specialtyId'],
            where: specialtyId ? { specialtyId } : {}
          }
        ]
      }
    ];

    const pricingList = await LawyerPricing.findAll({
      where: whereClause,
      include: includeClause,
      order: [['hourlyRate', 'ASC']],
      limit: 20
    });

    // 计算统计信息
    const stats = {
      count: pricingList.length,
      avgHourlyRate: 0,
      minHourlyRate: 0,
      maxHourlyRate: 0,
      avgBountyRate: 0
    };

    if (pricingList.length > 0) {
      const hourlyRates = pricingList.map(p => p.hourlyRate);
      const bountyRates = pricingList.map(p => p.bountyCommissionRate);

      stats.avgHourlyRate = Math.round(hourlyRates.reduce((a, b) => a + b, 0) / hourlyRates.length);
      stats.minHourlyRate = Math.min(...hourlyRates);
      stats.maxHourlyRate = Math.max(...hourlyRates);
      stats.avgBountyRate = Math.round(bountyRates.reduce((a, b) => a + b, 0) / bountyRates.length * 100) / 100;
    }

    res.json({
      success: true,
      data: {
        pricingList,
        stats
      }
    });
  } catch (error) {
    logger.error('获取律师收费标准列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取收费标准列表失败'
    });
  }
});

// 计算预估费用
router.post('/estimate', [
  body('lawyerId').isInt().withMessage('请选择律师'),
  body('serviceType').isIn(['consultation', 'document', 'representation', 'bounty']).withMessage('服务类型无效'),
  body('hours').optional().isFloat({ min: 0.5, max: 1000 }).withMessage('小时数0.5-1000'),
  body('caseAmount').optional().isFloat({ min: 0 }).withMessage('案件金额必须大于等于0'),
  body('documentCount').optional().isInt({ min: 1, max: 50 }).withMessage('文书数量1-50'),
  body('courtSessions').optional().isInt({ min: 1, max: 20 }).withMessage('出庭次数1-20')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const {
      lawyerId,
      serviceType,
      hours = 1,
      caseAmount = 0,
      documentCount = 1,
      courtSessions = 1
    } = req.body;

    const pricing = await LawyerPricing.findOne({
      where: { lawyerId, isActive: true }
    });

    if (!pricing) {
      return res.status(404).json({
        success: false,
        message: '律师收费标准未设置'
      });
    }

    let estimatedFee = 0;
    let breakdown = {};

    switch (serviceType) {
      case 'consultation':
        estimatedFee = pricing.consultationFee || pricing.hourlyRate * hours;
        breakdown = {
          consultationFee: pricing.consultationFee || 0,
          hourlyRate: pricing.hourlyRate,
          hours: hours,
          total: estimatedFee
        };
        break;

      case 'document':
        estimatedFee = pricing.documentFee * documentCount;
        breakdown = {
          documentFee: pricing.documentFee,
          documentCount: documentCount,
          total: estimatedFee
        };
        break;

      case 'representation':
        const baseFee = pricing.hourlyRate * hours;
        const courtFee = pricing.courtAppearanceFee * courtSessions;
        const documentFee = pricing.documentFee * documentCount;
        estimatedFee = baseFee + courtFee + documentFee;
        breakdown = {
          hourlyRate: pricing.hourlyRate,
          hours: hours,
          baseFee: baseFee,
          courtAppearanceFee: pricing.courtAppearanceFee,
          courtSessions: courtSessions,
          courtFee: courtFee,
          documentFee: pricing.documentFee,
          documentCount: documentCount,
          documentCost: documentFee,
          total: estimatedFee
        };
        break;

      case 'bounty':
        estimatedFee = caseAmount * (pricing.bountyCommissionRate / 100);
        breakdown = {
          caseAmount: caseAmount,
          commissionRate: pricing.bountyCommissionRate,
          commissionFee: estimatedFee,
          upfrontFee: 0,
          total: estimatedFee
        };
        break;

      default:
        return res.status(400).json({
          success: false,
          message: '不支持的服务类型'
        });
    }

    res.json({
      success: true,
      data: {
        serviceType,
        estimatedFee: Math.round(estimatedFee * 100) / 100,
        breakdown,
        paymentTerms: pricing.paymentTerms,
        discountPolicy: pricing.discountPolicy
      }
    });
  } catch (error) {
    logger.error('计算预估费用失败:', error);
    res.status(500).json({
      success: false,
      message: '计算预估费用失败'
    });
  }
});

module.exports = router;
