-- 起诉书生成系统模拟数据
-- 在执行此文件前，请先执行 schema.sql

USE lawsuit_system;

-- 清空现有数据（保留结构）
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE documents;
TRUNCATE TABLE templates;
TRUNCATE TABLE cases;
TRUNCATE TABLE case_types;
TRUNCATE TABLE operation_logs;
TRUNCATE TABLE file_uploads;
TRUNCATE TABLE users;
TRUNCATE TABLE system_configs;
SET FOREIGN_KEY_CHECKS = 1;

-- 插入用户数据
INSERT INTO users (username, email, password, real_name, phone, role, status, avatar, created_at, updated_at) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJK9fHPyy', '系统管理员', '13800000001', 'admin', 'active', NULL, NOW(), NOW()),
('lawyer_zhang', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJK9fHPyy', '张律师', '13800000002', 'lawyer', 'active', NULL, NOW(), NOW()),
('lawyer_li', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJK9fHPyy', '李律师', '13800000003', 'lawyer', 'active', NULL, NOW(), NOW()),
('user_wang', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJK9fHPyy', '王先生', '13800000004', 'user', 'active', NULL, NOW(), NOW()),
('user_chen', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJK9fHPyy', '陈女士', '13800000005', 'user', 'active', NULL, NOW(), NOW()),
('user_liu', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3QJK9fHPyy', '刘总', '13800000006', 'user', 'inactive', NULL, NOW(), NOW());

-- 插入案件类型数据（更详细的配置）
INSERT INTO case_types (name, code, description, category, template_fields, form_config, is_active, sort_order, created_at, updated_at) VALUES
('合同纠纷', 'CONTRACT_DISPUTE', '因合同履行产生的纠纷', 'civil', 
'{"contractType": "合同类型", "contractAmount": "合同金额", "signDate": "签订日期", "performanceDate": "履行期限", "breachDetails": "违约情况"}',
'{"sections": [{"title": "合同信息", "fields": ["contractType", "contractAmount", "signDate"]}, {"title": "违约情况", "fields": ["breachDetails"]}]}',
TRUE, 1, NOW(), NOW()),

('侵权责任纠纷', 'TORT_LIABILITY', '因侵权行为产生的责任纠纷', 'civil',
'{"tortType": "侵权类型", "damageAmount": "损失金额", "incidentDate": "事件日期", "damageDetails": "损害详情", "evidenceList": "证据清单"}',
'{"sections": [{"title": "侵权信息", "fields": ["tortType", "incidentDate"]}, {"title": "损害情况", "fields": ["damageAmount", "damageDetails"]}]}',
TRUE, 2, NOW(), NOW()),

('劳动合同纠纷', 'LABOR_CONTRACT', '因劳动合同履行产生的纠纷', 'labor',
'{"position": "工作岗位", "salary": "工资标准", "workPeriod": "工作期间", "dismissalReason": "解除原因", "compensation": "赔偿要求"}',
'{"sections": [{"title": "劳动关系", "fields": ["position", "salary", "workPeriod"]}, {"title": "争议情况", "fields": ["dismissalReason", "compensation"]}]}',
TRUE, 3, NOW(), NOW()),

('离婚纠纷', 'DIVORCE', '因离婚产生的纠纷', 'family',
'{"marriageDate": "结婚日期", "separationDate": "分居日期", "children": "子女情况", "property": "财产情况", "divorceReason": "离婚原因"}',
'{"sections": [{"title": "婚姻信息", "fields": ["marriageDate", "separationDate"]}, {"title": "争议焦点", "fields": ["children", "property"]}]}',
TRUE, 4, NOW(), NOW()),

('债权债务纠纷', 'DEBT_DISPUTE', '因债权债务关系产生的纠纷', 'civil',
'{"debtAmount": "债务金额", "loanDate": "借款日期", "repaymentDate": "还款期限", "interestRate": "利率", "guarantor": "担保人"}',
'{"sections": [{"title": "债务信息", "fields": ["debtAmount", "loanDate", "repaymentDate"]}, {"title": "其他信息", "fields": ["interestRate", "guarantor"]}]}',
TRUE, 5, NOW(), NOW());

-- 插入模板数据
INSERT INTO templates (case_type_id, name, description, content, variables, version, is_default, is_active, created_by, created_at, updated_at) VALUES
(1, '合同纠纷起诉书标准模板', '适用于一般合同纠纷案件的标准模板', 
'起 诉 书

原告：{{plaintiff.name}}，{{plaintiff.gender}}，{{plaintiff.age}}岁，{{plaintiff.nationality}}族，{{plaintiff.occupation}}，住址：{{plaintiff.address}}，联系电话：{{plaintiff.phone}}。

被告：{{defendant.name}}，{{defendant.gender}}，{{defendant.age}}岁，{{defendant.nationality}}族，{{defendant.occupation}}，住址：{{defendant.address}}，联系电话：{{defendant.phone}}。

诉讼请求：
{{#each claims}}
{{@index}}. {{this}}
{{/each}}

事实与理由：
{{facts}}

{{#if legalBasis}}
法律依据：
{{legalBasis}}
{{/if}}

{{#if evidence}}
证据清单：
{{evidence}}
{{/if}}

此致
{{court}}

起诉人：{{plaintiff.name}}
{{currentDate}}',
'{"plaintiff": "原告信息", "defendant": "被告信息", "claims": "诉讼请求", "facts": "事实与理由", "legalBasis": "法律依据", "evidence": "证据清单", "court": "法院", "currentDate": "当前日期"}',
'1.0', TRUE, TRUE, 2, NOW(), NOW()),

(2, '侵权责任纠纷起诉书模板', '适用于侵权责任纠纷案件', 
'起 诉 书

原告：{{plaintiff.name}}，{{plaintiff.gender}}，{{plaintiff.age}}岁，{{plaintiff.nationality}}族，{{plaintiff.occupation}}，住址：{{plaintiff.address}}，联系电话：{{plaintiff.phone}}。

被告：{{defendant.name}}，{{defendant.gender}}，{{defendant.age}}岁，{{defendant.nationality}}族，{{defendant.occupation}}，住址：{{defendant.address}}，联系电话：{{defendant.phone}}。

诉讼请求：
{{#each claims}}
{{@index}}. {{this}}
{{/each}}

事实与理由：
{{incidentDate}}，被告{{defendant.name}}{{tortDetails}}，给原告造成了{{damageAmount}}元的经济损失。{{facts}}

根据《中华人民共和国民法典》相关规定，被告应当承担侵权责任。

{{#if evidence}}
证据清单：
{{evidence}}
{{/if}}

此致
{{court}}

起诉人：{{plaintiff.name}}
{{currentDate}}',
'{"plaintiff": "原告信息", "defendant": "被告信息", "claims": "诉讼请求", "facts": "事实与理由", "incidentDate": "事件日期", "tortDetails": "侵权详情", "damageAmount": "损失金额", "evidence": "证据清单", "court": "法院", "currentDate": "当前日期"}',
'1.0', TRUE, TRUE, 2, NOW(), NOW()),

(3, '劳动合同纠纷起诉书模板', '适用于劳动合同纠纷案件', 
'起 诉 书

原告：{{plaintiff.name}}，{{plaintiff.gender}}，{{plaintiff.age}}岁，{{plaintiff.nationality}}族，{{plaintiff.occupation}}，住址：{{plaintiff.address}}，联系电话：{{plaintiff.phone}}。

被告：{{defendant.name}}，住址：{{defendant.address}}，法定代表人：{{defendant.legalRepresentative}}，联系电话：{{defendant.phone}}。

诉讼请求：
{{#each claims}}
{{@index}}. {{this}}
{{/each}}

事实与理由：
原告于{{workStartDate}}入职被告公司，担任{{position}}职务，月工资{{salary}}元。{{workPeriod}}期间，双方建立了劳动关系。{{dismissalDetails}}

被告的行为违反了《中华人民共和国劳动合同法》的相关规定，应当承担相应的法律责任。

{{#if evidence}}
证据清单：
{{evidence}}
{{/if}}

此致
{{court}}

起诉人：{{plaintiff.name}}
{{currentDate}}',
'{"plaintiff": "原告信息", "defendant": "被告信息", "claims": "诉讼请求", "facts": "事实与理由", "workStartDate": "入职日期", "position": "职务", "salary": "工资", "workPeriod": "工作期间", "dismissalDetails": "解除详情", "evidence": "证据清单", "court": "法院", "currentDate": "当前日期"}',
'1.0', TRUE, TRUE, 2, NOW(), NOW());

-- 插入案件数据
INSERT INTO cases (user_id, case_type_id, case_number, title, plaintiff_info, defendant_info, case_details, claims, facts_and_reasons, evidence_list, legal_basis, court_info, status, priority, tags, notes, created_at, updated_at) VALUES
(4, 1, 'CASE-2024001', '张三诉李四装修合同纠纷案', 
'{"name": "张三", "gender": "男", "age": 35, "nationality": "汉族", "occupation": "工程师", "address": "北京市朝阳区某某街道123号", "phone": "13800138000", "idNumber": "110101198801010001"}',
'{"name": "李四", "gender": "男", "age": 42, "nationality": "汉族", "occupation": "装修工", "address": "北京市海淀区某某路456号", "phone": "13900139000", "idNumber": "110101197801010002"}',
'{"contractType": "房屋装修合同", "contractAmount": 100000, "signDate": "2024-03-01", "performanceDate": "2024-06-01", "breachDetails": "装修质量不符合约定，存在严重质量问题"}',
'["请求被告返还装修费用人民币100,000元", "请求被告赔偿因装修质量问题造成的损失人民币20,000元", "请求被告承担本案诉讼费用"]',
'2024年3月1日，原告张三与被告李四签订《房屋装修合同》，约定被告为原告位于北京市朝阳区某某街道123号的房屋进行装修，装修费用为人民币10万元，工期为3个月。合同签订后，原告按约定支付了全部装修费用。装修期间，被告使用劣质材料，施工工艺粗糙，导致装修质量严重不符合合同约定和国家标准。2024年6月15日装修完工后，原告发现多处质量问题：墙面开裂、地板不平、水电安装不规范等。原告多次要求被告整改，但被告拒绝承担责任。',
'["《房屋装修合同》原件及复印件", "装修费用支付凭证（银行转账记录）", "装修质量问题照片", "专业机构出具的质量检测报告", "重新装修的费用发票", "与被告的沟通记录（微信聊天截图）"]',
'《中华人民共和国民法典》第五百七十七条：当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担继续履行、采取补救措施或者赔偿损失等违约责任。《中华人民共和国民法典》第五百八十四条：当事人一方不履行合同义务或者履行合同义务不符合约定，造成对方损失的，损失赔偿额应当相当于因违约所造成的损失。',
'{"court": "北京市朝阳区人民法院", "address": "北京市朝阳区建国门外大街甲6号", "phone": "010-65123456"}',
'completed', 'high', '["合同纠纷", "装修", "质量问题"]', '案件已完成，等待提交法院', NOW() - INTERVAL 5 DAY, NOW() - INTERVAL 1 DAY),

(5, 2, 'CASE-2024002', '陈女士诉某公司名誉权纠纷案',
'{"name": "陈女士", "gender": "女", "age": 28, "nationality": "汉族", "occupation": "市场经理", "address": "上海市浦东新区某某路789号", "phone": "13700137000", "idNumber": "310101199501010003"}',
'{"name": "某科技公司", "type": "company", "address": "上海市徐汇区某某大厦15楼", "legalRepresentative": "王总", "phone": "021-12345678", "creditCode": "91310000123456789X"}',
'{"tortType": "名誉权侵权", "damageAmount": 50000, "incidentDate": "2024-07-15", "damageDetails": "在公司内部群聊中恶意传播不实信息，损害原告名誉", "platform": "企业微信群"}',
'["请求被告立即停止侵权行为", "请求被告公开赔礼道歉", "请求被告赔偿精神损失费人民币50,000元", "请求被告承担本案诉讼费用"]',
'2024年7月15日，被告在企业微信群中发布不实信息，恶意诋毁原告的工作能力和个人品格，造成原告在同事中的名誉受损，工作环境恶化，精神受到严重伤害。原告多次要求被告删除不实信息并道歉，但被告拒绝配合。',
'["企业微信群聊天记录截图", "同事证言", "心理咨询记录", "工作受影响的相关证明"]',
'《中华人民共和国民法典》第一千零二十四条：民事主体享有名誉权。任何组织或者个人不得以侮辱、诽谤等方式侵害他人的名誉权。',
'{"court": "上海市浦东新区人民法院", "address": "上海市浦东新区世纪大道1号", "phone": "021-68123456"}',
'draft', 'medium', '["侵权", "名誉权", "网络侵权"]', '正在收集更多证据材料', NOW() - INTERVAL 3 DAY, NOW()),

(4, 3, 'CASE-2024003', '王先生诉某公司劳动合同纠纷案',
'{"name": "王先生", "gender": "男", "age": 32, "nationality": "汉族", "occupation": "软件工程师", "address": "深圳市南山区某某小区A栋", "phone": "13600136000", "idNumber": "******************"}',
'{"name": "某互联网公司", "type": "company", "address": "深圳市南山区科技园某某大厦", "legalRepresentative": "刘总", "phone": "0755-12345678", "creditCode": "91440300123456789Y"}',
'{"position": "高级软件工程师", "salary": 25000, "workPeriod": "2022年6月至2024年8月", "dismissalReason": "公司以业绩不达标为由解除劳动合同", "compensation": "要求支付违法解除劳动合同赔偿金"}',
'["请求确认被告违法解除劳动合同", "请求被告支付违法解除劳动合同赔偿金人民币100,000元", "请求被告支付拖欠的加班费人民币15,000元", "请求被告承担本案诉讼费用"]',
'原告于2022年6月1日入职被告公司，担任高级软件工程师，月工资25000元。工作期间，原告认真履行职责，工作表现良好。2024年8月15日，被告以业绩不达标为由单方面解除劳动合同，但未能提供充分的业绩考核依据。被告的解除行为违反了劳动合同法的相关规定。',
'["劳动合同", "工资发放记录", "工作成果证明", "同事证言", "解除通知书"]',
'《中华人民共和国劳动合同法》第四十八条：用人单位违反本法规定解除或者终止劳动合同，劳动者要求继续履行劳动合同的，用人单位应当继续履行；劳动者不要求继续履行劳动合同或者劳动合同已经不能继续履行的，用人单位应当依照本法第八十七条规定支付赔偿金。',
'{"court": "深圳市南山区人民法院", "address": "深圳市南山区南海大道某某号", "phone": "0755-26123456"}',
'submitted', 'high', '["劳动纠纷", "违法解除", "赔偿金"]', '案件已提交法院，等待开庭', NOW() - INTERVAL 7 DAY, NOW() - INTERVAL 2 DAY);

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description, data_type, is_public, created_at, updated_at) VALUES
('system_name', '起诉书生成系统', '系统名称', 'string', TRUE, NOW(), NOW()),
('system_version', '1.0.0', '系统版本', 'string', TRUE, NOW(), NOW()),
('max_file_size', '10485760', '最大文件上传大小(字节)', 'number', FALSE, NOW(), NOW()),
('allowed_file_types', '["jpg","jpeg","png","pdf","doc","docx"]', '允许的文件类型', 'json', FALSE, NOW(), NOW()),
('document_retention_days', '30', '文档保留天数', 'number', FALSE, NOW(), NOW()),
('enable_registration', 'true', '是否允许用户注册', 'boolean', TRUE, NOW(), NOW()),
('default_court', '北京市朝阳区人民法院', '默认法院', 'string', TRUE, NOW(), NOW()),
('contact_email', '<EMAIL>', '联系邮箱', 'string', TRUE, NOW(), NOW()),
('contact_phone', '************', '联系电话', 'string', TRUE, NOW(), NOW());

-- 插入操作日志
INSERT INTO operation_logs (user_id, action, resource_type, resource_id, details, ip_address, user_agent, created_at) VALUES
(4, 'CREATE_CASE', 'case', 1, '{"title": "张三诉李四装修合同纠纷案", "case_type": "合同纠纷"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 5 DAY),
(4, 'UPDATE_CASE', 'case', 1, '{"status": "completed"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 1 DAY),
(5, 'CREATE_CASE', 'case', 2, '{"title": "陈女士诉某公司名誉权纠纷案", "case_type": "侵权责任纠纷"}', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL 3 DAY),
(4, 'CREATE_CASE', 'case', 3, '{"title": "王先生诉某公司劳动合同纠纷案", "case_type": "劳动合同纠纷"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 7 DAY),
(4, 'UPDATE_CASE', 'case', 3, '{"status": "submitted"}', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 2 DAY),
(2, 'CREATE_TEMPLATE', 'template', 1, '{"name": "合同纠纷起诉书标准模板"}', '192.168.1.102', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 10 DAY),
(2, 'CREATE_TEMPLATE', 'template', 2, '{"name": "侵权责任纠纷起诉书模板"}', '192.168.1.102', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 9 DAY),
(1, 'LOGIN', 'user', 1, '{"login_method": "username"}', '192.168.1.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 1 HOUR);

-- 插入文件上传记录
INSERT INTO file_uploads (user_id, original_name, file_name, file_path, file_type, file_size, mime_type, purpose, status, created_at) VALUES
(4, '装修合同.pdf', 'contract_20240301_001.pdf', 'uploads/evidence/contract_20240301_001.pdf', 'pdf', 2048576, 'application/pdf', 'evidence', 'completed', NOW() - INTERVAL 5 DAY),
(4, '质量检测报告.pdf', 'quality_report_001.pdf', 'uploads/evidence/quality_report_001.pdf', 'pdf', 1536000, 'application/pdf', 'evidence', 'completed', NOW() - INTERVAL 4 DAY),
(5, '聊天记录截图.png', 'chat_screenshot_001.png', 'uploads/evidence/chat_screenshot_001.png', 'png', 512000, 'image/png', 'evidence', 'completed', NOW() - INTERVAL 3 DAY),
(4, '劳动合同.docx', 'labor_contract_001.docx', 'uploads/evidence/labor_contract_001.docx', 'docx', 1024000, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'evidence', 'completed', NOW() - INTERVAL 7 DAY);

-- 更新用户最后登录时间
UPDATE users SET last_login_at = NOW() - INTERVAL 1 HOUR WHERE id = 1;
UPDATE users SET last_login_at = NOW() - INTERVAL 2 HOUR WHERE id = 2;
UPDATE users SET last_login_at = NOW() - INTERVAL 6 HOUR WHERE id = 4;
UPDATE users SET last_login_at = NOW() - INTERVAL 1 DAY WHERE id = 5;
