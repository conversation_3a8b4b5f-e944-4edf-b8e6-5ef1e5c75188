document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('lawsuitForm');
    const resultSection = document.getElementById('result');
    const lawsuitContent = document.getElementById('lawsuitContent');

    // 表单提交处理
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        generateLawsuit();
    });

    // 根据当事人类型显示/隐藏相关字段
    document.getElementById('plaintiffType').addEventListener('change', function() {
        togglePersonalFields('plaintiff', this.value);
    });

    document.getElementById('defendantType').addEventListener('change', function() {
        togglePersonalFields('defendant', this.value);
    });

    function togglePersonalFields(party, type) {
        const genderField = document.getElementById(party + 'Gender').parentElement;
        const ageField = document.getElementById(party + 'Age').parentElement;
        const nationalityField = document.getElementById(party + 'Nationality').parentElement;
        const occupationField = document.getElementById(party + 'Occupation').parentElement;

        if (type === 'individual') {
            genderField.style.display = 'block';
            ageField.style.display = 'block';
            nationalityField.style.display = 'block';
            occupationField.style.display = 'block';
        } else {
            genderField.style.display = 'none';
            ageField.style.display = 'none';
            nationalityField.style.display = 'none';
            occupationField.style.display = 'none';
        }
    }

    function generateLawsuit() {
        const formData = new FormData(form);
        const data = {};
        
        // 收集表单数据
        for (let [key, value] of formData.entries()) {
            data[key] = value.trim();
        }

        // 验证必填字段
        if (!validateForm(data)) {
            return;
        }

        // 生成起诉书内容
        const lawsuitText = createLawsuitDocument(data);
        
        // 显示结果
        lawsuitContent.innerHTML = lawsuitText;
        resultSection.style.display = 'block';
        
        // 滚动到结果区域
        resultSection.scrollIntoView({ behavior: 'smooth' });
    }

    function validateForm(data) {
        const requiredFields = [
            'plaintiffName', 'plaintiffType', 'plaintiffAddress',
            'defendantName', 'defendantType', 'defendantAddress',
            'caseType', 'claims', 'facts', 'court'
        ];

        for (let field of requiredFields) {
            if (!data[field]) {
                alert(`请填写${getFieldLabel(field)}`);
                document.getElementById(field).focus();
                return false;
            }
        }
        return true;
    }

    function getFieldLabel(fieldName) {
        const labels = {
            'plaintiffName': '原告姓名/名称',
            'plaintiffType': '原告类型',
            'plaintiffAddress': '原告住址/地址',
            'defendantName': '被告姓名/名称',
            'defendantType': '被告类型',
            'defendantAddress': '被告住址/地址',
            'caseType': '案件类型',
            'claims': '诉讼请求',
            'facts': '事实经过',
            'court': '受理法院'
        };
        return labels[fieldName] || fieldName;
    }

    function createLawsuitDocument(data) {
        const currentDate = new Date().toLocaleDateString('zh-CN');
        
        let lawsuit = `<h3>起 诉 书</h3>\n\n`;
        
        // 当事人信息
        lawsuit += formatPartyInfo('原告', data, 'plaintiff');
        lawsuit += '\n';
        lawsuit += formatPartyInfo('被告', data, 'defendant');
        lawsuit += '\n';

        // 诉讼请求
        lawsuit += `<div class="section-title">诉讼请求：</div>\n`;
        lawsuit += formatClaims(data.claims);
        lawsuit += '\n';

        // 事实与理由
        lawsuit += `<div class="section-title">事实与理由：</div>\n`;
        lawsuit += data.facts;
        
        if (data.legalBasis) {
            lawsuit += '\n\n根据以下法律条文：\n';
            lawsuit += data.legalBasis;
        }
        lawsuit += '\n';

        // 证据清单
        if (data.evidence) {
            lawsuit += `<div class="section-title">证据清单：</div>\n`;
            lawsuit += data.evidence;
            lawsuit += '\n';
        }

        // 结尾
        lawsuit += `\n综上所述，被告的行为已经严重损害了原告的合法权益，请求人民法院依法支持原告的诉讼请求。\n\n`;
        lawsuit += `此致\n`;
        lawsuit += `${data.court}\n\n`;
        
        // 签名区域
        lawsuit += `<div class="signature-area">`;
        lawsuit += `起诉人：${data.plaintiffName}\n`;
        lawsuit += `${currentDate}`;
        lawsuit += `</div>`;

        return lawsuit;
    }

    function formatPartyInfo(partyLabel, data, prefix) {
        let info = `${partyLabel}：${data[prefix + 'Name']}`;
        
        if (data[prefix + 'Type'] === 'individual') {
            if (data[prefix + 'Gender']) info += `，${data[prefix + 'Gender']}`;
            if (data[prefix + 'Age']) info += `，${data[prefix + 'Age']}岁`;
            if (data[prefix + 'Nationality']) info += `，${data[prefix + 'Nationality']}族`;
            if (data[prefix + 'Occupation']) info += `，${data[prefix + 'Occupation']}`;
        }
        
        info += `，住址：${data[prefix + 'Address']}`;
        
        if (data[prefix + 'Phone']) {
            info += `，联系电话：${data[prefix + 'Phone']}`;
        }
        
        if (data[prefix + 'IdNumber']) {
            const idLabel = data[prefix + 'Type'] === 'individual' ? '身份证号' : '统一社会信用代码';
            info += `，${idLabel}：${data[prefix + 'IdNumber']}`;
        }
        
        info += '。';
        return info;
    }

    function formatClaims(claims) {
        // 如果用户已经格式化了请求，直接返回
        if (claims.includes('1.') || claims.includes('一、')) {
            return claims;
        }
        
        // 否则尝试自动格式化
        const lines = claims.split('\n').filter(line => line.trim());
        if (lines.length === 1) {
            return `1. ${lines[0]}`;
        }
        
        return lines.map((line, index) => `${index + 1}. ${line.trim()}`).join('\n');
    }
});

// 打印功能
function printLawsuit() {
    window.print();
}

// 下载功能
function downloadLawsuit() {
    const content = document.getElementById('lawsuitContent').innerText;
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '起诉书.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 复制功能
function copyLawsuit() {
    const content = document.getElementById('lawsuitContent').innerText;
    navigator.clipboard.writeText(content).then(function() {
        alert('起诉书内容已复制到剪贴板');
    }).catch(function(err) {
        console.error('复制失败:', err);
        // 备用复制方法
        const textArea = document.createElement('textarea');
        textArea.value = content;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('起诉书内容已复制到剪贴板');
    });
}
