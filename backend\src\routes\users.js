const express = require('express');
const { body, validationResult } = require('express-validator');
const { User } = require('../models');
const { auth, adminOnly } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// 获取用户列表 (仅管理员)
router.get('/', auth, adminOnly, async (req, res) => {
  try {
    const { page = 1, limit = 10, search, role, status } = req.query;
    const offset = (page - 1) * limit;

    const where = {};
    if (search) {
      where.$or = [
        { username: { $like: `%${search}%` } },
        { email: { $like: `%${search}%` } },
        { realName: { $like: `%${search}%` } }
      ];
    }
    if (role) where.role = role;
    if (status) where.status = status;

    const { count, rows: users } = await User.findAndCountAll({
      where,
      attributes: { exclude: ['password'] },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']]
    });

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    logger.error('获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败'
    });
  }
});

// 获取用户详情
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // 非管理员只能查看自己的信息
    if (req.user.role !== 'admin' && req.user.id !== parseInt(id)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const user = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: { user }
    });
  } catch (error) {
    logger.error('获取用户详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户详情失败'
    });
  }
});

// 创建用户 (仅管理员)
router.post('/', auth, adminOnly, [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .isAlphanumeric()
    .withMessage('用户名只能包含字母和数字'),
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符'),
  body('role')
    .isIn(['admin', 'lawyer', 'user'])
    .withMessage('角色必须是admin、lawyer或user')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { username, email, password, realName, phone, role } = req.body;

    // 检查用户名和邮箱是否已存在
    const existingUser = await User.findOne({
      where: {
        $or: [{ username }, { email }]
      }
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: existingUser.username === username ? '用户名已存在' : '邮箱已存在'
      });
    }

    const user = await User.create({
      username,
      email,
      password,
      realName,
      phone,
      role
    });

    logger.info(`管理员创建用户: ${username}`, { adminId: req.user.id });

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: { user: user.toJSON() }
    });
  } catch (error) {
    logger.error('创建用户失败:', error);
    res.status(500).json({
      success: false,
      message: '创建用户失败'
    });
  }
});

// 更新用户 (管理员或用户本人)
router.put('/:id', auth, [
  body('realName')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('真实姓名长度必须在2-50个字符之间'),
  body('phone')
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('请输入有效的手机号码'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('role')
    .optional()
    .isIn(['admin', 'lawyer', 'user'])
    .withMessage('角色必须是admin、lawyer或user'),
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended'])
    .withMessage('状态必须是active、inactive或suspended')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { realName, phone, email, role, status } = req.body;

    // 权限检查
    const isAdmin = req.user.role === 'admin';
    const isSelf = req.user.id === parseInt(id);

    if (!isAdmin && !isSelf) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 非管理员不能修改角色和状态
    if (!isAdmin && (role || status)) {
      return res.status(403).json({
        success: false,
        message: '权限不足，无法修改角色或状态'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查邮箱是否已被其他用户使用
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '邮箱已被其他用户使用'
        });
      }
    }

    const updateData = {};
    if (realName !== undefined) updateData.realName = realName;
    if (phone !== undefined) updateData.phone = phone;
    if (email !== undefined) updateData.email = email;
    if (isAdmin && role !== undefined) updateData.role = role;
    if (isAdmin && status !== undefined) updateData.status = status;

    await user.update(updateData);

    logger.info(`用户信息更新: ${user.username}`, { 
      updatedBy: req.user.id,
      isAdmin,
      updateData 
    });

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: { user: user.toJSON() }
    });
  } catch (error) {
    logger.error('更新用户失败:', error);
    res.status(500).json({
      success: false,
      message: '更新用户失败'
    });
  }
});

// 删除用户 (仅管理员)
router.delete('/:id', auth, adminOnly, async (req, res) => {
  try {
    const { id } = req.params;

    // 不能删除自己
    if (req.user.id === parseInt(id)) {
      return res.status(400).json({
        success: false,
        message: '不能删除自己的账户'
      });
    }

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    await user.destroy();

    logger.info(`管理员删除用户: ${user.username}`, { adminId: req.user.id });

    res.json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    logger.error('删除用户失败:', error);
    res.status(500).json({
      success: false,
      message: '删除用户失败'
    });
  }
});

module.exports = router;
