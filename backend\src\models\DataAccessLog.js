module.exports = (sequelize, DataTypes) => {
  const DataAccessLog = sequelize.define('DataAccessLog', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id'
    },
    targetUserId: {
      type: DataTypes.INTEGER,
      field: 'target_user_id'
    },
    resourceType: {
      type: DataTypes.ENUM('case', 'user_info', 'contact', 'document'),
      allowNull: false,
      field: 'resource_type'
    },
    resourceId: {
      type: DataTypes.INTEGER,
      field: 'resource_id'
    },
    accessType: {
      type: DataTypes.ENUM('view', 'edit', 'download', 'export'),
      allowNull: false,
      field: 'access_type'
    },
    accessLevel: {
      type: DataTypes.ENUM('full', 'summary', 'limited'),
      allowNull: false,
      field: 'access_level'
    },
    authorizationId: {
      type: DataTypes.INTEGER,
      field: 'authorization_id'
    },
    ipAddress: {
      type: DataTypes.STRING(45),
      field: 'ip_address'
    },
    userAgent: {
      type: DataTypes.TEXT,
      field: 'user_agent'
    },
    accessResult: {
      type: DataTypes.ENUM('success', 'denied', 'error'),
      defaultValue: 'success',
      field: 'access_result'
    },
    denialReason: {
      type: DataTypes.STRING(255),
      field: 'denial_reason'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    }
  }, {
    tableName: 'data_access_logs',
    timestamps: false,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        fields: ['target_user_id']
      },
      {
        fields: ['resource_type', 'resource_id']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['access_result']
      }
    ]
  });

  // 类方法
  DataAccessLog.logAccess = async function(logData) {
    try {
      return await this.create(logData);
    } catch (error) {
      console.error('记录访问日志失败:', error);
      return null;
    }
  };

  DataAccessLog.getAccessStats = async function(userId, timeRange = '7d') {
    const timeMap = {
      '1d': 1,
      '7d': 7,
      '30d': 30,
      '90d': 90
    };

    const days = timeMap[timeRange] || 7;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const stats = await this.findAll({
      where: {
        userId,
        createdAt: { [sequelize.Op.gte]: startDate }
      },
      attributes: [
        'resourceType',
        'accessResult',
        [sequelize.fn('COUNT', '*'), 'count']
      ],
      group: ['resourceType', 'accessResult'],
      raw: true
    });

    return stats;
  };

  DataAccessLog.getSecurityReport = async function(timeRange = '30d') {
    const timeMap = {
      '1d': 1,
      '7d': 7,
      '30d': 30,
      '90d': 90
    };

    const days = timeMap[timeRange] || 30;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // 获取拒绝访问统计
    const deniedAccess = await this.findAll({
      where: {
        accessResult: 'denied',
        createdAt: { [sequelize.Op.gte]: startDate }
      },
      attributes: [
        'userId',
        'resourceType',
        'denialReason',
        [sequelize.fn('COUNT', '*'), 'count']
      ],
      group: ['userId', 'resourceType', 'denialReason'],
      include: [
        {
          model: sequelize.models.User,
          as: 'user',
          attributes: ['username', 'realName']
        }
      ],
      order: [[sequelize.literal('count'), 'DESC']],
      limit: 50
    });

    // 获取高频访问用户
    const highFrequencyUsers = await this.findAll({
      where: {
        createdAt: { [sequelize.Op.gte]: startDate }
      },
      attributes: [
        'userId',
        [sequelize.fn('COUNT', '*'), 'accessCount'],
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('target_user_id'))), 'uniqueTargets']
      ],
      group: ['userId'],
      include: [
        {
          model: sequelize.models.User,
          as: 'user',
          attributes: ['username', 'realName', 'role']
        }
      ],
      having: sequelize.literal('accessCount > 100'),
      order: [[sequelize.literal('accessCount'), 'DESC']],
      limit: 20
    });

    return {
      deniedAccess,
      highFrequencyUsers,
      timeRange: `${days}天`
    };
  };

  DataAccessLog.getUserAccessHistory = async function(userId, options = {}) {
    const { limit = 50, offset = 0, resourceType, accessResult } = options;
    
    const where = { userId };
    if (resourceType) where.resourceType = resourceType;
    if (accessResult) where.accessResult = accessResult;

    return await this.findAndCountAll({
      where,
      include: [
        {
          model: sequelize.models.User,
          as: 'targetUser',
          attributes: ['username', 'realName']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
  };

  // 关联定义
  DataAccessLog.associate = function(models) {
    DataAccessLog.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    DataAccessLog.belongsTo(models.User, {
      foreignKey: 'targetUserId',
      as: 'targetUser'
    });
    DataAccessLog.belongsTo(models.UserLawyerAuthorization, {
      foreignKey: 'authorizationId',
      as: 'authorization'
    });
  };

  return DataAccessLog;
};
