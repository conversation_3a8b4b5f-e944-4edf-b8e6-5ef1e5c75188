{"name": "lawsuit-system-backend", "version": "1.0.0", "description": "起诉书生成系统后端API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "migrate": "sequelize-cli db:migrate", "migrate:undo": "sequelize-cli db:migrate:undo", "seed": "sequelize-cli db:seed:all", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["lawsuit", "legal", "document", "generator", "api"], "author": "Lawsuit System Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.1", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "sequelize-cli": "^6.6.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.1", "docx": "^8.2.2", "html-pdf": "^3.0.1", "handlebars": "^4.7.7", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.0", "dotenv": "^16.3.1", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "nodemailer": "^6.9.4", "redis": "^4.6.7", "ioredis": "^5.3.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.1", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5", "@types/jest": "^29.5.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}