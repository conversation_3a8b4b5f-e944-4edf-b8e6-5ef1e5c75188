module.exports = (sequelize, DataTypes) => {
  const OperationLog = sequelize.define('OperationLog', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      field: 'user_id'
    },
    action: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    resourceType: {
      type: DataTypes.STRING(50),
      field: 'resource_type'
    },
    resourceId: {
      type: DataTypes.INTEGER,
      field: 'resource_id'
    },
    details: {
      type: DataTypes.JSON,
      defaultValue: {}
    },
    ipAddress: {
      type: DataTypes.STRING(45),
      field: 'ip_address'
    },
    userAgent: {
      type: DataTypes.TEXT,
      field: 'user_agent'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    }
  }, {
    tableName: 'operation_logs',
    timestamps: false
  });

  // 关联定义
  OperationLog.associate = function(models) {
    OperationLog.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return OperationLog;
};
