-- 起诉书生成系统数据库结构
-- MySQL 8.0+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS lawsuit_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE lawsuit_system;

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码哈希',
    real_name VARCHAR(50) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    role ENUM('admin', 'lawyer', 'user') DEFAULT 'user' COMMENT '角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
    avatar VARCHAR(255) COMMENT '头像URL',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
) COMMENT '用户表';

-- 案件类型表
CREATE TABLE case_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '类型名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '类型代码',
    description TEXT COMMENT '描述',
    category ENUM('civil', 'labor', 'family', 'intellectual', 'administrative', 'criminal') NOT NULL COMMENT '案件分类',
    template_fields JSON COMMENT '模板字段定义',
    form_config JSON COMMENT '表单配置',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_category (category),
    INDEX idx_active (is_active)
) COMMENT '案件类型表';

-- 案件表
CREATE TABLE cases (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    case_type_id INT NOT NULL COMMENT '案件类型ID',
    case_number VARCHAR(100) UNIQUE COMMENT '案件编号',
    title VARCHAR(200) NOT NULL COMMENT '案件标题',
    plaintiff_info JSON NOT NULL COMMENT '原告信息',
    defendant_info JSON NOT NULL COMMENT '被告信息',
    case_details JSON NOT NULL COMMENT '案件详情',
    claims JSON NOT NULL COMMENT '诉讼请求',
    facts_and_reasons TEXT COMMENT '事实与理由',
    evidence_list JSON COMMENT '证据清单',
    legal_basis TEXT COMMENT '法律依据',
    court_info JSON COMMENT '法院信息',
    status ENUM('draft', 'completed', 'submitted', 'archived') DEFAULT 'draft' COMMENT '状态',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    tags JSON COMMENT '标签',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (case_type_id) REFERENCES case_types(id) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_case_type_id (case_type_id),
    INDEX idx_case_number (case_number),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT '案件表';

-- 文档模板表
CREATE TABLE templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    case_type_id INT NOT NULL COMMENT '案件类型ID',
    name VARCHAR(100) NOT NULL COMMENT '模板名称',
    description TEXT COMMENT '模板描述',
    content LONGTEXT NOT NULL COMMENT '模板内容',
    variables JSON COMMENT '变量定义',
    version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认模板',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_by INT COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (case_type_id) REFERENCES case_types(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_case_type_id (case_type_id),
    INDEX idx_is_default (is_default),
    INDEX idx_is_active (is_active)
) COMMENT '文档模板表';

-- 生成文档表
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    case_id INT NOT NULL COMMENT '案件ID',
    template_id INT NOT NULL COMMENT '模板ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_type ENUM('docx', 'pdf', 'html') NOT NULL COMMENT '文件类型',
    file_size INT COMMENT '文件大小(字节)',
    content LONGTEXT COMMENT '文档内容',
    generation_params JSON COMMENT '生成参数',
    status ENUM('generating', 'completed', 'failed') DEFAULT 'generating' COMMENT '生成状态',
    error_message TEXT COMMENT '错误信息',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP COMMENT '过期时间',
    FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES templates(id) ON DELETE RESTRICT,
    INDEX idx_case_id (case_id),
    INDEX idx_template_id (template_id),
    INDEX idx_status (status),
    INDEX idx_generated_at (generated_at)
) COMMENT '生成文档表';

-- 系统配置表
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description TEXT COMMENT '描述',
    data_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '数据类型',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) COMMENT '系统配置表';

-- 操作日志表
CREATE TABLE operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id INT COMMENT '资源ID',
    details JSON COMMENT '操作详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created_at (created_at)
) COMMENT '操作日志表';

-- 文件上传表
CREATE TABLE file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_type VARCHAR(100) COMMENT '文件类型',
    file_size INT COMMENT '文件大小',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    purpose ENUM('avatar', 'evidence', 'template', 'other') DEFAULT 'other' COMMENT '用途',
    status ENUM('uploading', 'completed', 'failed') DEFAULT 'uploading' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_purpose (purpose),
    INDEX idx_status (status)
) COMMENT '文件上传表';

-- 插入默认案件类型数据
INSERT INTO case_types (name, code, description, category, is_active) VALUES
('合同纠纷', 'CONTRACT_DISPUTE', '因合同履行产生的纠纷', 'civil', TRUE),
('侵权责任纠纷', 'TORT_LIABILITY', '因侵权行为产生的责任纠纷', 'civil', TRUE),
('物权纠纷', 'PROPERTY_RIGHTS', '因物权归属和行使产生的纠纷', 'civil', TRUE),
('债权债务纠纷', 'DEBT_DISPUTE', '因债权债务关系产生的纠纷', 'civil', TRUE),
('劳动合同纠纷', 'LABOR_CONTRACT', '因劳动合同履行产生的纠纷', 'labor', TRUE),
('工伤赔偿纠纷', 'WORK_INJURY', '因工伤赔偿产生的纠纷', 'labor', TRUE),
('劳动报酬纠纷', 'LABOR_PAYMENT', '因劳动报酬支付产生的纠纷', 'labor', TRUE),
('离婚纠纷', 'DIVORCE', '因离婚产生的纠纷', 'family', TRUE),
('财产分割纠纷', 'PROPERTY_DIVISION', '因财产分割产生的纠纷', 'family', TRUE),
('子女抚养纠纷', 'CHILD_CUSTODY', '因子女抚养产生的纠纷', 'family', TRUE),
('著作权纠纷', 'COPYRIGHT', '因著作权产生的纠纷', 'intellectual', TRUE),
('商标权纠纷', 'TRADEMARK', '因商标权产生的纠纷', 'intellectual', TRUE),
('专利权纠纷', 'PATENT', '因专利权产生的纠纷', 'intellectual', TRUE),
('行政诉讼', 'ADMINISTRATIVE_LITIGATION', '对行政行为不服提起的诉讼', 'administrative', TRUE);

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, description, data_type, is_public) VALUES
('system_name', '起诉书生成系统', '系统名称', 'string', TRUE),
('system_version', '1.0.0', '系统版本', 'string', TRUE),
('max_file_size', '10485760', '最大文件上传大小(字节)', 'number', FALSE),
('allowed_file_types', '["jpg","jpeg","png","pdf","doc","docx"]', '允许的文件类型', 'json', FALSE),
('document_retention_days', '30', '文档保留天数', 'number', FALSE),
('enable_registration', 'true', '是否允许用户注册', 'boolean', TRUE);

-- 创建默认管理员用户 (密码: admin123)
INSERT INTO users (username, email, password, real_name, role, status) VALUES
('admin', '<EMAIL>', '$2b$10$rQZ8kHWKQYXHQVQXQVQXQeQVQXQVQXQVQXQVQXQVQXQVQXQVQXQVQ', '系统管理员', 'admin', 'active');
