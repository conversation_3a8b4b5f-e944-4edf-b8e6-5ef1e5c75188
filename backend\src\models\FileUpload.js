module.exports = (sequelize, DataTypes) => {
  const FileUpload = sequelize.define('FileUpload', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id'
    },
    originalName: {
      type: DataTypes.STRING(255),
      allowNull: false,
      field: 'original_name'
    },
    fileName: {
      type: DataTypes.STRING(255),
      allowNull: false,
      field: 'file_name'
    },
    filePath: {
      type: DataTypes.STRING(500),
      allowNull: false,
      field: 'file_path'
    },
    fileType: {
      type: DataTypes.STRING(100),
      field: 'file_type'
    },
    fileSize: {
      type: DataTypes.INTEGER,
      field: 'file_size'
    },
    mimeType: {
      type: DataTypes.STRING(100),
      field: 'mime_type'
    },
    purpose: {
      type: DataTypes.ENUM('avatar', 'evidence', 'template', 'other'),
      defaultValue: 'other'
    },
    status: {
      type: DataTypes.ENUM('uploading', 'completed', 'failed'),
      defaultValue: 'uploading'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    }
  }, {
    tableName: 'file_uploads',
    timestamps: false
  });

  // 关联定义
  FileUpload.associate = function(models) {
    FileUpload.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return FileUpload;
};
