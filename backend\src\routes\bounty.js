const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { 
  BountyCase, 
  BountyApplication, 
  CaseType, 
  LawyerSpecialty,
  LawyerProfile,
  User 
} = require('../models');
const { auth, lawyer<PERSON>r<PERSON>dmin } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// 获取悬赏案件列表
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须大于0'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量1-50'),
  query('caseType').optional().isInt().withMessage('案件类型ID必须是整数'),
  query('specialty').optional().isInt().withMessage('专业ID必须是整数'),
  query('location').optional().isLength({ min: 1, max: 50 }).withMessage('地区长度1-50字符'),
  query('minAmount').optional().isFloat({ min: 0 }).withMessage('最低金额必须大于0'),
  query('maxAmount').optional().isFloat({ min: 0 }).withMessage('最高金额必须大于0'),
  query('urgency').optional().isIn(['low', 'medium', 'high', 'urgent']).withMessage('紧急程度无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 10,
      caseType,
      specialty,
      location,
      minAmount,
      maxAmount,
      urgency
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { status: 'open' };

    // 构建查询条件
    if (caseType) where.caseTypeId = caseType;
    if (specialty) where.requiredSpecialtyId = specialty;
    if (location) where.location = { [require('sequelize').Op.like]: `%${location}%` };
    if (urgency) where.caseUrgency = urgency;
    if (minAmount || maxAmount) {
      where.expectedCompensation = {};
      if (minAmount) where.expectedCompensation[require('sequelize').Op.gte] = minAmount;
      if (maxAmount) where.expectedCompensation[require('sequelize').Op.lte] = maxAmount;
    }

    const { count, rows: bountyCases } = await BountyCase.findAndCountAll({
      where,
      include: [
        {
          model: CaseType,
          as: 'caseType',
          attributes: ['id', 'name', 'category']
        },
        {
          model: LawyerSpecialty,
          as: 'requiredSpecialty',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'realName']
        }
      ],
      order: [['isFeatured', 'DESC'], ['expectedCompensation', 'DESC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        bountyCases,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });
  } catch (error) {
    logger.error('获取悬赏案件列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取悬赏案件列表失败'
    });
  }
});

// 获取悬赏案件详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const bountyCase = await BountyCase.findByPk(id, {
      include: [
        {
          model: CaseType,
          as: 'caseType'
        },
        {
          model: LawyerSpecialty,
          as: 'requiredSpecialty'
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'realName']
        },
        {
          model: User,
          as: 'assignedLawyer',
          attributes: ['id', 'realName'],
          include: [
            {
              model: LawyerProfile,
              as: 'lawyerProfile'
            }
          ]
        }
      ]
    });

    if (!bountyCase) {
      return res.status(404).json({
        success: false,
        message: '悬赏案件不存在'
      });
    }

    // 增加浏览次数
    await bountyCase.increment('viewCount');

    res.json({
      success: true,
      data: { bountyCase }
    });
  } catch (error) {
    logger.error('获取悬赏案件详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取悬赏案件详情失败'
    });
  }
});

// 发布悬赏委托
router.post('/', auth, [
  body('caseTypeId').isInt().withMessage('请选择案件类型'),
  body('title').isLength({ min: 5, max: 200 }).withMessage('标题长度5-200字符'),
  body('description').isLength({ min: 20, max: 2000 }).withMessage('描述长度20-2000字符'),
  body('disputeAmount').isFloat({ min: 0 }).withMessage('争议金额必须大于0'),
  body('expectedCompensation').isFloat({ min: 0 }).withMessage('预期赔偿金额必须大于0'),
  body('commissionRate').isFloat({ min: 5, max: 50 }).withMessage('分成比例5%-50%'),
  body('location').optional().isLength({ max: 100 }).withMessage('地区最多100字符'),
  body('deadline').optional().isISO8601().withMessage('截止时间格式无效'),
  body('caseUrgency').optional().isIn(['low', 'medium', 'high', 'urgent']).withMessage('紧急程度无效'),
  body('caseComplexity').optional().isIn(['simple', 'medium', 'complex', 'very_complex']).withMessage('复杂程度无效'),
  body('evidenceStrength').optional().isIn(['weak', 'medium', 'strong', 'very_strong']).withMessage('证据强度无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const {
      caseTypeId,
      title,
      description,
      disputeAmount,
      expectedCompensation,
      commissionRate,
      location,
      deadline,
      caseUrgency,
      caseComplexity,
      evidenceStrength,
      additionalRequirements
    } = req.body;

    // 验证案件类型
    const caseType = await CaseType.findByPk(caseTypeId);
    if (!caseType || !caseType.isActive) {
      return res.status(400).json({
        success: false,
        message: '案件类型不存在或已停用'
      });
    }

    // 获取对应的专业要求
    const requiredSpecialty = caseType.templateFields?.required_specialty;
    let requiredSpecialtyId = null;
    if (requiredSpecialty) {
      const specialty = await LawyerSpecialty.findOne({
        where: { code: requiredSpecialty }
      });
      if (specialty) {
        requiredSpecialtyId = specialty.id;
      }
    }

    // 创建悬赏委托
    const bountyCase = await BountyCase.create({
      userId,
      caseTypeId,
      title,
      description,
      disputeAmount,
      expectedCompensation,
      commissionRate,
      requiredSpecialtyId,
      location,
      deadline: deadline ? new Date(deadline) : null,
      caseUrgency: caseUrgency || 'medium',
      caseComplexity: caseComplexity || 'medium',
      evidenceStrength: evidenceStrength || 'medium',
      additionalRequirements,
      status: 'open'
    });

    logger.info(`用户发布悬赏委托: ${title}`, { 
      userId, 
      bountyId: bountyCase.id,
      expectedCompensation,
      commissionRate 
    });

    res.status(201).json({
      success: true,
      message: '悬赏委托发布成功',
      data: { bountyCase }
    });
  } catch (error) {
    logger.error('发布悬赏委托失败:', error);
    res.status(500).json({
      success: false,
      message: '发布悬赏委托失败'
    });
  }
});

// 申请接案
router.post('/:id/apply', auth, lawyerOrAdmin, [
  body('applicationMessage').isLength({ min: 10, max: 1000 }).withMessage('申请说明10-1000字符'),
  body('proposedStrategy').optional().isLength({ max: 2000 }).withMessage('拟定策略最多2000字符'),
  body('estimatedDuration').optional().isInt({ min: 1, max: 365 }).withMessage('预估周期1-365天'),
  body('successProbability').optional().isFloat({ min: 0, max: 100 }).withMessage('胜诉概率0-100%'),
  body('similarCasesCount').optional().isInt({ min: 0 }).withMessage('类似案件数量必须大于等于0'),
  body('commissionRate').optional().isFloat({ min: 5, max: 50 }).withMessage('期望分成比例5%-50%')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const lawyerId = req.user.id;
    const {
      applicationMessage,
      proposedStrategy,
      estimatedDuration,
      successProbability,
      similarCasesCount,
      commissionRate
    } = req.body;

    // 检查悬赏案件
    const bountyCase = await BountyCase.findByPk(id);
    if (!bountyCase) {
      return res.status(404).json({
        success: false,
        message: '悬赏案件不存在'
      });
    }

    if (bountyCase.status !== 'open') {
      return res.status(400).json({
        success: false,
        message: '该案件已不接受申请'
      });
    }

    // 检查律师资格
    const lawyerProfile = await LawyerProfile.findByUser(lawyerId);
    if (!lawyerProfile || !lawyerProfile.isVerified || lawyerProfile.status !== 'verified') {
      return res.status(403).json({
        success: false,
        message: '您不是认证律师，无法申请接案'
      });
    }

    // 检查专业匹配
    if (bountyCase.requiredSpecialtyId && lawyerProfile.specialtyId !== bountyCase.requiredSpecialtyId) {
      return res.status(400).json({
        success: false,
        message: '您的专业与案件要求不匹配'
      });
    }

    // 检查是否已申请
    const existingApplication = await BountyApplication.findOne({
      where: { bountyCaseId: id, lawyerId }
    });

    if (existingApplication) {
      return res.status(400).json({
        success: false,
        message: '您已申请过该案件'
      });
    }

    // 创建申请
    const application = await BountyApplication.create({
      bountyCaseId: id,
      lawyerId,
      applicationMessage,
      proposedStrategy,
      estimatedDuration,
      successProbability,
      similarCasesCount,
      commissionRate: commissionRate || bountyCase.commissionRate,
      status: 'pending'
    });

    // 增加申请次数
    await bountyCase.increment('applicationCount');

    logger.info(`律师申请接案`, { 
      lawyerId, 
      bountyId: id,
      applicationId: application.id 
    });

    res.status(201).json({
      success: true,
      message: '申请提交成功，请等待委托人确认',
      data: { application }
    });
  } catch (error) {
    logger.error('申请接案失败:', error);
    res.status(500).json({
      success: false,
      message: '申请接案失败'
    });
  }
});

// 获取案件申请列表（委托人查看）
router.get('/:id/applications', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 检查权限
    const bountyCase = await BountyCase.findByPk(id);
    if (!bountyCase) {
      return res.status(404).json({
        success: false,
        message: '悬赏案件不存在'
      });
    }

    if (bountyCase.userId !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const applications = await BountyApplication.findAll({
      where: { bountyCaseId: id },
      include: [
        {
          model: User,
          as: 'lawyer',
          attributes: ['id', 'realName', 'avatar'],
          include: [
            {
              model: LawyerProfile,
              as: 'lawyerProfile'
            }
          ]
        }
      ],
      order: [['appliedAt', 'DESC']]
    });

    res.json({
      success: true,
      data: { applications }
    });
  } catch (error) {
    logger.error('获取申请列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取申请列表失败'
    });
  }
});

// 确认律师接案
router.put('/:id/assign/:applicationId', auth, async (req, res) => {
  try {
    const { id, applicationId } = req.params;
    const userId = req.user.id;

    // 检查悬赏案件
    const bountyCase = await BountyCase.findByPk(id);
    if (!bountyCase) {
      return res.status(404).json({
        success: false,
        message: '悬赏案件不存在'
      });
    }

    if (bountyCase.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: '只有委托人可以确认接案'
      });
    }

    if (bountyCase.status !== 'open') {
      return res.status(400).json({
        success: false,
        message: '该案件已不接受申请'
      });
    }

    // 检查申请
    const application = await BountyApplication.findByPk(applicationId);
    if (!application || application.bountyCaseId !== parseInt(id)) {
      return res.status(404).json({
        success: false,
        message: '申请不存在'
      });
    }

    // 更新案件状态
    await bountyCase.update({
      status: 'assigned',
      assignedLawyerId: application.lawyerId,
      assignedAt: new Date()
    });

    // 更新申请状态
    await application.update({
      status: 'accepted',
      respondedAt: new Date()
    });

    // 拒绝其他申请
    await BountyApplication.update(
      { 
        status: 'rejected',
        respondedAt: new Date(),
        responseMessage: '委托人已选择其他律师'
      },
      {
        where: {
          bountyCaseId: id,
          id: { [require('sequelize').Op.ne]: applicationId },
          status: 'pending'
        }
      }
    );

    logger.info(`确认律师接案`, { 
      userId, 
      bountyId: id,
      lawyerId: application.lawyerId,
      applicationId 
    });

    res.json({
      success: true,
      message: '律师接案确认成功'
    });
  } catch (error) {
    logger.error('确认律师接案失败:', error);
    res.status(500).json({
      success: false,
      message: '确认律师接案失败'
    });
  }
});

module.exports = router;
