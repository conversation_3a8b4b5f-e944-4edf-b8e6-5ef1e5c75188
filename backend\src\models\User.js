const bcrypt = require('bcryptjs');

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 50],
        isAlphanumeric: true
      }
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        len: [6, 255]
      }
    },
    realName: {
      type: DataTypes.STRING(50),
      field: 'real_name'
    },
    phone: {
      type: DataTypes.STRING(20),
      validate: {
        is: /^1[3-9]\d{9}$/
      }
    },
    role: {
      type: DataTypes.ENUM('admin', 'lawyer', 'user'),
      defaultValue: 'user'
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'suspended'),
      defaultValue: 'active'
    },
    avatar: {
      type: DataTypes.STRING(255)
    },
    lastLoginAt: {
      type: DataTypes.DATE,
      field: 'last_login_at'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'updated_at'
    }
  }, {
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    hooks: {
      beforeCreate: async (user) => {
        if (user.password) {
          user.password = await bcrypt.hash(user.password, 12);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, 12);
        }
      }
    }
  });

  // 实例方法
  User.prototype.validatePassword = async function(password) {
    return bcrypt.compare(password, this.password);
  };

  User.prototype.toJSON = function() {
    const values = Object.assign({}, this.get());
    delete values.password;
    return values;
  };

  User.prototype.updateLastLogin = async function() {
    this.lastLoginAt = new Date();
    await this.save();
  };

  // 类方法
  User.findByEmail = function(email) {
    return this.findOne({ where: { email } });
  };

  User.findByUsername = function(username) {
    return this.findOne({ where: { username } });
  };

  User.findActive = function() {
    return this.findAll({ where: { status: 'active' } });
  };

  // 关联定义
  User.associate = function(models) {
    User.hasMany(models.Case, {
      foreignKey: 'userId',
      as: 'cases'
    });
    User.hasMany(models.Template, {
      foreignKey: 'createdBy',
      as: 'templates'
    });
    User.hasMany(models.OperationLog, {
      foreignKey: 'userId',
      as: 'operationLogs'
    });
    User.hasMany(models.FileUpload, {
      foreignKey: 'userId',
      as: 'fileUploads'
    });
  };

  return User;
};
