<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>起诉书生成系统 - 数据安全演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            padding: 30px 0;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .security-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .security-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .security-card:hover {
            transform: translateY(-5px);
        }

        .security-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
            padding-left: 25px;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }

        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .role-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .role-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #3498db;
        }

        .role-card.admin {
            border-left-color: #e74c3c;
        }

        .role-card.lawyer {
            border-left-color: #f39c12;
        }

        .role-card.user {
            border-left-color: #27ae60;
        }

        .access-level {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
            margin: 2px;
        }

        .access-full {
            background: #d4edda;
            color: #155724;
        }

        .access-limited {
            background: #fff3cd;
            color: #856404;
        }

        .access-summary {
            background: #cce5ff;
            color: #004085;
        }

        .access-denied {
            background: #f8d7da;
            color: #721c24;
        }

        .data-example {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9em;
        }

        .masked {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .specialty-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .specialty-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .authorization-flow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .flow-step {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin: 5px;
            flex: 1;
            min-width: 150px;
            text-align: center;
        }

        .flow-arrow {
            font-size: 1.5em;
            color: #3498db;
            margin: 0 10px;
        }

        @media (max-width: 768px) {
            .security-grid {
                grid-template-columns: 1fr;
            }
            
            .role-demo {
                grid-template-columns: 1fr;
            }
            
            .authorization-flow {
                flex-direction: column;
            }
            
            .flow-arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 数据安全保障机制</h1>
            <p>律师专业化管理 · 用户授权控制 · 数据访问监控</p>
        </div>

        <div class="security-grid">
            <div class="security-card">
                <h3>🛡️ 多层权限控制</h3>
                <ul class="feature-list">
                    <li>基于角色的访问控制(RBAC)</li>
                    <li>用户主动授权机制</li>
                    <li>律师专业匹配验证</li>
                    <li>数据访问级别分层</li>
                    <li>实时权限验证</li>
                </ul>
            </div>

            <div class="security-card">
                <h3>👨‍⚖️ 律师专业化管理</h3>
                <ul class="feature-list">
                    <li>律师执业资格认证</li>
                    <li>专业领域限制</li>
                    <li>案件类型匹配</li>
                    <li>业绩统计展示</li>
                    <li>持续监督管理</li>
                </ul>
            </div>

            <div class="security-card">
                <h3>🔐 数据脱敏保护</h3>
                <ul class="feature-list">
                    <li>敏感信息自动脱敏</li>
                    <li>分级数据展示</li>
                    <li>联系方式保护</li>
                    <li>身份信息加密</li>
                    <li>访问日志记录</li>
                </ul>
            </div>

            <div class="security-card">
                <h3>📊 安全监控审计</h3>
                <ul class="feature-list">
                    <li>全量访问日志</li>
                    <li>异常行为检测</li>
                    <li>权限变更追踪</li>
                    <li>安全报告生成</li>
                    <li>合规性检查</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2>律师专业分类</h2>
            <p>系统支持7大专业领域，确保专业对口的法律服务</p>
            
            <div class="specialty-grid">
                <div class="specialty-card">
                    <h4>合同法律师</h4>
                    <p>CONTRACT_LAW</p>
                </div>
                <div class="specialty-card">
                    <h4>侵权法律师</h4>
                    <p>TORT_LAW</p>
                </div>
                <div class="specialty-card">
                    <h4>劳动法律师</h4>
                    <p>LABOR_LAW</p>
                </div>
                <div class="specialty-card">
                    <h4>婚姻家庭律师</h4>
                    <p>FAMILY_LAW</p>
                </div>
                <div class="specialty-card">
                    <h4>知识产权律师</h4>
                    <p>IP_LAW</p>
                </div>
                <div class="specialty-card">
                    <h4>行政法律师</h4>
                    <p>ADMIN_LAW</p>
                </div>
                <div class="specialty-card">
                    <h4>刑事辩护律师</h4>
                    <p>CRIMINAL_LAW</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>用户授权流程</h2>
            <p>用户可以精确控制律师的访问权限</p>
            
            <div class="authorization-flow">
                <div class="flow-step">
                    <strong>1. 选择律师</strong><br>
                    根据专业匹配推荐
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <strong>2. 设置权限</strong><br>
                    选择授权类型和范围
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <strong>3. 确认授权</strong><br>
                    设置有效期和备注
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step">
                    <strong>4. 监控访问</strong><br>
                    实时查看访问日志
                </div>
            </div>

            <h3>授权类型说明</h3>
            <div class="role-demo">
                <div class="role-card">
                    <h4>🔓 完全授权 (Full)</h4>
                    <ul>
                        <li>查看完整案件信息</li>
                        <li>查看联系方式</li>
                        <li>编辑案件内容</li>
                        <li>生成法律文档</li>
                        <li>直接沟通联系</li>
                    </ul>
                </div>
                <div class="role-card">
                    <h4>🔒 有限授权 (Limited)</h4>
                    <ul>
                        <li>查看部分脱敏信息</li>
                        <li>查看脱敏联系方式</li>
                        <li>只读案件内容</li>
                        <li>生成法律文档</li>
                        <li>通过平台沟通</li>
                    </ul>
                </div>
                <div class="role-card">
                    <h4>👁️ 仅查看 (View Only)</h4>
                    <ul>
                        <li>查看案件摘要</li>
                        <li>无法查看联系方式</li>
                        <li>无法编辑内容</li>
                        <li>无法生成文档</li>
                        <li>仅平台内沟通</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>数据访问级别演示</h2>
            <p>根据不同角色和授权级别，系统自动调整数据展示</p>

            <div class="role-demo">
                <div class="role-card admin">
                    <h4>👑 系统管理员</h4>
                    <span class="access-level access-full">完全访问</span>
                    <div class="data-example">
原告：张三，男，35岁，汉族，工程师
住址：北京市朝阳区某某街道123号
电话：13800138000
身份证：110101198801010001
                    </div>
                </div>

                <div class="role-card lawyer">
                    <h4>⚖️ 授权律师</h4>
                    <span class="access-level access-limited">有限访问</span>
                    <div class="data-example">
原告：张三，男，35岁，汉族，工程师
住址：北京市朝阳区某某街道123号
电话：<span class="masked">138****8000</span>
身份证：<span class="masked">1101**********0001</span>
                    </div>
                </div>

                <div class="role-card lawyer">
                    <h4>👨‍💼 专业匹配律师</h4>
                    <span class="access-level access-summary">摘要访问</span>
                    <div class="data-example">
原告：<span class="masked">张**</span>，个人
案件类型：合同纠纷
争议金额：约10万元
案件摘要：装修合同履行纠纷...
<span style="color: #999;">（联系方式已隐藏）</span>
                    </div>
                </div>

                <div class="role-card user">
                    <h4>👤 非专业律师</h4>
                    <span class="access-level access-denied">拒绝访问</span>
                    <div class="data-example">
<span style="color: #e74c3c;">❌ 权限不足</span>
您的专业不匹配此案件类型
无法查看案件详情
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>安全监控功能</h2>
            <p>全方位的安全监控和审计功能</p>

            <div class="role-demo">
                <div class="role-card">
                    <h4>📈 访问统计</h4>
                    <ul>
                        <li>每日访问量统计</li>
                        <li>资源访问分布</li>
                        <li>用户行为分析</li>
                        <li>异常访问检测</li>
                    </ul>
                    <button class="btn btn-primary" onclick="showAccessStats()">查看统计</button>
                </div>

                <div class="role-card">
                    <h4>🔍 访问日志</h4>
                    <ul>
                        <li>详细访问记录</li>
                        <li>权限验证日志</li>
                        <li>数据脱敏记录</li>
                        <li>失败访问追踪</li>
                    </ul>
                    <button class="btn btn-success" onclick="showAccessLogs()">查看日志</button>
                </div>

                <div class="role-card">
                    <h4>⚠️ 安全警报</h4>
                    <ul>
                        <li>异常访问模式</li>
                        <li>权限滥用检测</li>
                        <li>数据泄露风险</li>
                        <li>合规性检查</li>
                    </ul>
                    <button class="btn btn-warning" onclick="showSecurityAlerts()">查看警报</button>
                </div>

                <div class="role-card">
                    <h4>📊 合规报告</h4>
                    <ul>
                        <li>数据保护合规</li>
                        <li>访问权限审计</li>
                        <li>安全策略评估</li>
                        <li>风险评估报告</li>
                    </ul>
                    <button class="btn btn-danger" onclick="showComplianceReport()">生成报告</button>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>技术实现特色</h2>
            <div class="security-grid">
                <div class="security-card">
                    <h3>🔐 加密存储</h3>
                    <p>敏感数据采用AES-256加密存储，密钥分离管理，确保数据安全。</p>
                </div>

                <div class="security-card">
                    <h3>🚦 实时验证</h3>
                    <p>每次数据访问都进行实时权限验证，支持动态权限调整。</p>
                </div>

                <div class="security-card">
                    <h3>📝 完整审计</h3>
                    <p>记录所有数据访问操作，支持事后审计和合规检查。</p>
                </div>

                <div class="security-card">
                    <h3>🛡️ 多重防护</h3>
                    <p>IP限制、设备绑定、异常检测等多重安全防护机制。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showAccessStats() {
            alert('访问统计功能：\n\n📊 数据概览\n- 今日访问：156次\n- 本周访问：1,234次\n- 成功率：98.5%\n- 拒绝访问：18次\n\n📈 趋势分析\n- 访问量稳定增长\n- 律师活跃度提升\n- 用户授权增加');
        }

        function showAccessLogs() {
            alert('访问日志示例：\n\n🕐 2024-02-04 15:30:25\n👤 张律师 (lawyer_zhang)\n📋 查看案件 CASE-2024001\n✅ 成功 (有限访问)\n🌐 IP: *************\n\n🕐 2024-02-04 15:28:15\n👤 李律师 (lawyer_li)\n📋 尝试查看案件 CASE-2024001\n❌ 拒绝 (专业不匹配)\n🌐 IP: *************');
        }

        function showSecurityAlerts() {
            alert('安全警报：\n\n⚠️ 高频访问检测\n用户 lawyer_wang 在1小时内访问了50个不同案件\n建议：检查是否存在异常行为\n\n🔍 权限异常\n用户尝试访问超出授权范围的数据\n已自动拒绝并记录日志\n\n📊 数据导出异常\n检测到大量数据导出操作\n建议：审查导出权限设置');
        }

        function showComplianceReport() {
            alert('合规报告生成：\n\n📋 数据保护合规性\n✅ 敏感数据加密率：100%\n✅ 访问日志完整性：100%\n✅ 权限最小化原则：符合\n✅ 数据脱敏机制：正常\n\n📊 风险评估\n🟢 低风险：数据访问控制\n🟡 中风险：第三方集成\n🔴 高风险：暂无\n\n📈 改进建议\n- 定期权限审查\n- 加强用户培训\n- 完善监控机制');
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.security-card, .role-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
