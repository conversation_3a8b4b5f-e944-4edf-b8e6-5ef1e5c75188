const { User, Case, LawyerProfile, UserLawyerAuthorization, DataAccessLog } = require('../models');
const logger = require('../utils/logger');

// 数据访问权限检查中间件
const checkDataAccess = (resourceType, accessType = 'view') => {
  return async (req, res, next) => {
    try {
      const userId = req.user.id;
      const userRole = req.user.role;
      const resourceId = req.params.id;
      const targetUserId = req.params.userId || req.body.userId;

      // 管理员拥有所有权限
      if (userRole === 'admin') {
        await logDataAccess(userId, targetUserId, resourceType, resourceId, accessType, 'full', 'success');
        return next();
      }

      let accessLevel = 'denied';
      let denialReason = '';

      switch (resourceType) {
        case 'case':
          accessLevel = await checkCaseAccess(userId, resourceId, accessType);
          break;
        case 'user_info':
          accessLevel = await checkUserInfoAccess(userId, targetUserId, accessType);
          break;
        case 'contact':
          accessLevel = await checkContactAccess(userId, targetUserId, accessType);
          break;
        case 'document':
          accessLevel = await checkDocumentAccess(userId, resourceId, accessType);
          break;
        default:
          accessLevel = 'denied';
          denialReason = '未知资源类型';
      }

      if (accessLevel === 'denied') {
        await logDataAccess(userId, targetUserId, resourceType, resourceId, accessType, accessLevel, 'denied', denialReason);
        return res.status(403).json({
          success: false,
          message: '权限不足',
          reason: denialReason || '您没有访问此资源的权限'
        });
      }

      // 记录成功访问
      await logDataAccess(userId, targetUserId, resourceType, resourceId, accessType, accessLevel, 'success');

      // 将访问级别添加到请求对象
      req.accessLevel = accessLevel;
      next();

    } catch (error) {
      logger.error('数据访问权限检查失败:', error);
      await logDataAccess(req.user?.id, null, resourceType, req.params.id, accessType, 'denied', 'error', error.message);
      res.status(500).json({
        success: false,
        message: '权限检查失败'
      });
    }
  };
};

// 检查案件访问权限
async function checkCaseAccess(lawyerId, caseId, accessType) {
  try {
    // 获取案件信息
    const caseInfo = await Case.findByPk(caseId, {
      include: [
        {
          model: require('../models').CaseType,
          as: 'caseType'
        }
      ]
    });

    if (!caseInfo) {
      return 'denied';
    }

    // 如果是案件所有者
    if (caseInfo.userId === lawyerId) {
      return 'full';
    }

    // 检查律师资格和专业匹配
    const lawyerProfile = await LawyerProfile.findOne({
      where: { 
        userId: lawyerId, 
        isVerified: true,
        status: 'verified'
      },
      include: [
        {
          model: require('../models').LawyerSpecialty,
          as: 'specialty'
        }
      ]
    });

    if (!lawyerProfile) {
      return 'denied';
    }

    // 检查专业匹配
    const requiredSpecialty = caseInfo.caseType.templateFields?.required_specialty;
    if (requiredSpecialty && lawyerProfile.specialty.code !== requiredSpecialty) {
      return 'summary'; // 专业不匹配，只能查看摘要
    }

    // 检查用户授权
    const authorization = await UserLawyerAuthorization.findOne({
      where: {
        userId: caseInfo.userId,
        lawyerId: lawyerId,
        status: 'active',
        $or: [
          { caseId: caseId },
          { caseId: null } // 全局授权
        ]
      },
      order: [['caseId', 'DESC']] // 优先特定案件授权
    });

    if (authorization) {
      // 检查授权是否过期
      if (authorization.expiresAt && new Date() > authorization.expiresAt) {
        return 'summary';
      }

      // 根据授权类型返回访问级别
      switch (authorization.authorizationType) {
        case 'full':
          return 'full';
        case 'limited':
          return 'limited';
        case 'view_only':
          return 'summary';
        default:
          return 'summary';
      }
    }

    // 默认返回摘要级别（专业匹配但无授权）
    return 'summary';

  } catch (error) {
    logger.error('检查案件访问权限失败:', error);
    return 'denied';
  }
}

// 检查用户信息访问权限
async function checkUserInfoAccess(requesterId, targetUserId, accessType) {
  try {
    // 自己的信息
    if (requesterId === targetUserId) {
      return 'full';
    }

    // 检查是否有授权
    const authorization = await UserLawyerAuthorization.findOne({
      where: {
        userId: targetUserId,
        lawyerId: requesterId,
        status: 'active'
      }
    });

    if (authorization) {
      const permissions = authorization.permissions || {};
      if (permissions.view_contact) {
        return authorization.authorizationType === 'full' ? 'full' : 'limited';
      }
    }

    return 'denied';

  } catch (error) {
    logger.error('检查用户信息访问权限失败:', error);
    return 'denied';
  }
}

// 检查联系方式访问权限
async function checkContactAccess(requesterId, targetUserId, accessType) {
  try {
    // 自己的联系方式
    if (requesterId === targetUserId) {
      return 'full';
    }

    // 检查律师授权
    const authorization = await UserLawyerAuthorization.findOne({
      where: {
        userId: targetUserId,
        lawyerId: requesterId,
        status: 'active'
      }
    });

    if (authorization) {
      const permissions = authorization.permissions || {};
      if (permissions.view_contact) {
        return 'limited'; // 联系方式只提供有限访问
      }
    }

    return 'denied';

  } catch (error) {
    logger.error('检查联系方式访问权限失败:', error);
    return 'denied';
  }
}

// 检查文档访问权限
async function checkDocumentAccess(requesterId, documentId, accessType) {
  try {
    const document = await require('../models').Document.findByPk(documentId, {
      include: [
        {
          model: Case,
          as: 'case'
        }
      ]
    });

    if (!document) {
      return 'denied';
    }

    // 检查案件访问权限
    return await checkCaseAccess(requesterId, document.case.id, accessType);

  } catch (error) {
    logger.error('检查文档访问权限失败:', error);
    return 'denied';
  }
}

// 记录数据访问日志
async function logDataAccess(userId, targetUserId, resourceType, resourceId, accessType, accessLevel, result, denialReason = null) {
  try {
    await DataAccessLog.create({
      userId,
      targetUserId,
      resourceType,
      resourceId,
      accessType,
      accessLevel,
      accessResult: result,
      denialReason,
      ipAddress: '127.0.0.1', // 在实际使用中从请求中获取
      userAgent: 'System' // 在实际使用中从请求中获取
    });
  } catch (error) {
    logger.error('记录数据访问日志失败:', error);
  }
}

// 数据脱敏中间件
const sanitizeData = (req, res, next) => {
  const originalJson = res.json;
  
  res.json = function(data) {
    if (req.accessLevel && req.accessLevel !== 'full') {
      data = sanitizeResponseData(data, req.accessLevel);
    }
    return originalJson.call(this, data);
  };
  
  next();
};

// 根据访问级别脱敏数据
function sanitizeResponseData(data, accessLevel) {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const sanitized = JSON.parse(JSON.stringify(data));

  if (accessLevel === 'summary') {
    // 摘要级别：隐藏敏感信息
    if (sanitized.plaintiffInfo) {
      sanitized.plaintiffInfo = {
        name: maskName(sanitized.plaintiffInfo.name),
        type: sanitized.plaintiffInfo.type
      };
      delete sanitized.plaintiffInfo.phone;
      delete sanitized.plaintiffInfo.address;
      delete sanitized.plaintiffInfo.idNumber;
    }

    if (sanitized.defendantInfo) {
      sanitized.defendantInfo = {
        name: maskName(sanitized.defendantInfo.name),
        type: sanitized.defendantInfo.type
      };
      delete sanitized.defendantInfo.phone;
      delete sanitized.defendantInfo.address;
      delete sanitized.defendantInfo.idNumber;
    }

    // 限制事实描述长度
    if (sanitized.factsAndReasons) {
      sanitized.factsAndReasons = sanitized.factsAndReasons.substring(0, 200) + '...';
    }

    // 移除证据清单
    delete sanitized.evidenceList;
    delete sanitized.legalBasis;

  } else if (accessLevel === 'limited') {
    // 有限级别：部分脱敏
    if (sanitized.plaintiffInfo?.phone) {
      sanitized.plaintiffInfo.phone = maskPhone(sanitized.plaintiffInfo.phone);
    }
    if (sanitized.plaintiffInfo?.idNumber) {
      sanitized.plaintiffInfo.idNumber = maskIdNumber(sanitized.plaintiffInfo.idNumber);
    }
    if (sanitized.defendantInfo?.phone) {
      sanitized.defendantInfo.phone = maskPhone(sanitized.defendantInfo.phone);
    }
    if (sanitized.defendantInfo?.idNumber) {
      sanitized.defendantInfo.idNumber = maskIdNumber(sanitized.defendantInfo.idNumber);
    }
  }

  return sanitized;
}

// 姓名脱敏
function maskName(name) {
  if (!name || name.length <= 1) return name;
  return name.charAt(0) + '*'.repeat(name.length - 1);
}

// 电话脱敏
function maskPhone(phone) {
  if (!phone || phone.length < 7) return phone;
  return phone.substring(0, 3) + '****' + phone.substring(phone.length - 4);
}

// 身份证脱敏
function maskIdNumber(idNumber) {
  if (!idNumber || idNumber.length < 8) return idNumber;
  return idNumber.substring(0, 4) + '**********' + idNumber.substring(idNumber.length - 4);
}

module.exports = {
  checkDataAccess,
  sanitizeData,
  checkCaseAccess,
  checkUserInfoAccess,
  checkContactAccess,
  logDataAccess
};
