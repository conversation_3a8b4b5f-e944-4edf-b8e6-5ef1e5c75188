module.exports = (sequelize, DataTypes) => {
  const LawyerSpecialty = sequelize.define('LawyerSpecialty', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100]
      }
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 50],
        isUppercase: true
      }
    },
    description: {
      type: DataTypes.TEXT
    },
    category: {
      type: DataTypes.ENUM('civil', 'labor', 'family', 'intellectual', 'administrative', 'criminal'),
      allowNull: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'updated_at'
    }
  }, {
    tableName: 'lawyer_specialties',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['code']
      },
      {
        fields: ['category']
      }
    ]
  });

  // 实例方法
  LawyerSpecialty.prototype.activate = async function() {
    this.isActive = true;
    await this.save();
  };

  LawyerSpecialty.prototype.deactivate = async function() {
    this.isActive = false;
    await this.save();
  };

  // 类方法
  LawyerSpecialty.findActive = function() {
    return this.findAll({
      where: { isActive: true },
      order: [['name', 'ASC']]
    });
  };

  LawyerSpecialty.findByCategory = function(category) {
    return this.findAll({
      where: { category, isActive: true },
      order: [['name', 'ASC']]
    });
  };

  LawyerSpecialty.findByCode = function(code) {
    return this.findOne({ where: { code } });
  };

  // 关联定义
  LawyerSpecialty.associate = function(models) {
    LawyerSpecialty.hasMany(models.LawyerProfile, {
      foreignKey: 'specialtyId',
      as: 'lawyers'
    });
  };

  return LawyerSpecialty;
};
