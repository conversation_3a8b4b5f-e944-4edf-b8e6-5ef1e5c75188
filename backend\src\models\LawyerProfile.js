module.exports = (sequelize, DataTypes) => {
  const LawyerProfile = sequelize.define('LawyerProfile', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id'
    },
    specialtyId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'specialty_id'
    },
    licenseNumber: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      field: 'license_number'
    },
    lawFirm: {
      type: DataTypes.STRING(200),
      field: 'law_firm'
    },
    practiceYears: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'practice_years'
    },
    caseCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      field: 'case_count'
    },
    successRate: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0.00,
      field: 'success_rate'
    },
    education: {
      type: DataTypes.TEXT
    },
    achievements: {
      type: DataTypes.TEXT
    },
    introduction: {
      type: DataTypes.TEXT
    },
    hourlyRate: {
      type: DataTypes.DECIMAL(10, 2),
      field: 'hourly_rate'
    },
    isVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_verified'
    },
    verificationDate: {
      type: DataTypes.DATE,
      field: 'verification_date'
    },
    verifiedBy: {
      type: DataTypes.INTEGER,
      field: 'verified_by'
    },
    status: {
      type: DataTypes.ENUM('pending', 'verified', 'rejected', 'suspended'),
      defaultValue: 'pending'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'updated_at'
    }
  }, {
    tableName: 'lawyer_profiles',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['license_number']
      },
      {
        unique: true,
        fields: ['user_id']
      },
      {
        fields: ['specialty_id']
      },
      {
        fields: ['is_verified']
      },
      {
        fields: ['status']
      }
    ]
  });

  // 实例方法
  LawyerProfile.prototype.verify = async function(adminId) {
    this.isVerified = true;
    this.status = 'verified';
    this.verificationDate = new Date();
    this.verifiedBy = adminId;
    await this.save();
  };

  LawyerProfile.prototype.reject = async function(adminId) {
    this.isVerified = false;
    this.status = 'rejected';
    this.verifiedBy = adminId;
    await this.save();
  };

  LawyerProfile.prototype.suspend = async function(adminId) {
    this.status = 'suspended';
    this.verifiedBy = adminId;
    await this.save();
  };

  LawyerProfile.prototype.updateCaseStats = async function(caseCount, successRate) {
    this.caseCount = caseCount;
    this.successRate = successRate;
    await this.save();
  };

  LawyerProfile.prototype.getPublicProfile = function() {
    return {
      id: this.id,
      userId: this.userId,
      specialtyId: this.specialtyId,
      lawFirm: this.lawFirm,
      practiceYears: this.practiceYears,
      caseCount: this.caseCount,
      successRate: this.successRate,
      education: this.education,
      achievements: this.achievements,
      introduction: this.introduction,
      hourlyRate: this.hourlyRate,
      isVerified: this.isVerified,
      status: this.status
    };
  };

  // 类方法
  LawyerProfile.findVerified = function(options = {}) {
    return this.findAll({
      where: { 
        isVerified: true, 
        status: 'verified',
        ...options.where 
      },
      include: [
        {
          model: sequelize.models.User,
          as: 'user',
          attributes: ['id', 'username', 'realName', 'avatar']
        },
        {
          model: sequelize.models.LawyerSpecialty,
          as: 'specialty'
        }
      ],
      order: [['successRate', 'DESC'], ['caseCount', 'DESC']],
      ...options
    });
  };

  LawyerProfile.findBySpecialty = function(specialtyId, options = {}) {
    return this.findVerified({
      where: { specialtyId },
      ...options
    });
  };

  LawyerProfile.findByUser = function(userId) {
    return this.findOne({
      where: { userId },
      include: [
        {
          model: sequelize.models.LawyerSpecialty,
          as: 'specialty'
        }
      ]
    });
  };

  LawyerProfile.searchLawyers = function(query, options = {}) {
    const { specialty, location, minSuccessRate, maxHourlyRate } = query;
    const where = { isVerified: true, status: 'verified' };

    if (specialty) where.specialtyId = specialty;
    if (minSuccessRate) where.successRate = { [sequelize.Op.gte]: minSuccessRate };
    if (maxHourlyRate) where.hourlyRate = { [sequelize.Op.lte]: maxHourlyRate };

    const include = [
      {
        model: sequelize.models.User,
        as: 'user',
        attributes: ['id', 'username', 'realName', 'avatar']
      },
      {
        model: sequelize.models.LawyerSpecialty,
        as: 'specialty'
      }
    ];

    if (location) {
      include[0].where = {
        [sequelize.Op.or]: [
          { realName: { [sequelize.Op.like]: `%${location}%` } }
        ]
      };
    }

    return this.findAll({
      where,
      include,
      order: [['successRate', 'DESC'], ['caseCount', 'DESC']],
      ...options
    });
  };

  // 关联定义
  LawyerProfile.associate = function(models) {
    LawyerProfile.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    LawyerProfile.belongsTo(models.LawyerSpecialty, {
      foreignKey: 'specialtyId',
      as: 'specialty'
    });
    LawyerProfile.belongsTo(models.User, {
      foreignKey: 'verifiedBy',
      as: 'verifier'
    });
    LawyerProfile.hasMany(models.UserLawyerAuthorization, {
      foreignKey: 'lawyerId',
      as: 'authorizations'
    });
  };

  return LawyerProfile;
};
